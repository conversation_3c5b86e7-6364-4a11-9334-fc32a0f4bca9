const User = require('../models/userModel');

class EnhancedQuizMarkingService {
  constructor() {
    this.difficultyMultipliers = {
      easy: 1.0,
      medium: 1.5,
      hard: 2.0
    };
    
    this.questionTypeMultipliers = {
      mcq: 1.0,
      fill: 1.2,
      image: 1.3,
      text_based: 1.1
    };
  }

  /**
   * Calculate enhanced score for a quiz attempt
   * @param {Object} params - Quiz marking parameters
   * @param {Array} params.questions - Array of questions with answers
   * @param {Object} params.selectedOptions - User's selected answers
   * @param {Object} params.gptMap - AI evaluation results for free text questions
   * @param {number} params.timeSpent - Time spent on quiz in seconds
   * @param {number} params.totalTimeAllowed - Total time allowed for quiz
   * @param {Object} params.user - User object
   * @param {Object} params.examData - Exam configuration
   * @returns {Object} Enhanced quiz result
   */
  async calculateEnhancedScore(params) {
    const {
      questions,
      selectedOptions,
      gptMap = {},
      timeSpent,
      totalTimeAllowed,
      user,
      examData
    } = params;

    const results = {
      correctAnswers: [],
      wrongAnswers: [],
      partialAnswers: [],
      totalQuestions: questions.length,
      baseScore: 0,
      bonusPoints: 0,
      penaltyPoints: 0,
      finalScore: 0,
      finalPoints: 0,
      verdict: 'Fail',
      breakdown: {
        difficultyBonus: 0,
        timeBonus: 0,
        streakBonus: 0,
        consistencyBonus: 0,
        improvementBonus: 0
      },
      achievements: []
    };

    // Process each question
    let consecutiveCorrect = 0;
    let maxStreak = 0;
    let totalDifficultyPoints = 0;
    let totalPossiblePoints = 0;

    for (let idx = 0; idx < questions.length; idx++) {
      const question = questions[idx];
      const userAnswer = selectedOptions[idx] || "";
      const questionResult = this.evaluateQuestion(question, userAnswer, gptMap);
      
      // Calculate base points for this question
      const difficultyMultiplier = this.difficultyMultipliers[question.difficultyLevel] || 1.0;
      const typeMultiplier = this.questionTypeMultipliers[question.questionType] || 1.0;
      const baseQuestionPoints = 10 * difficultyMultiplier * typeMultiplier;
      
      totalPossiblePoints += baseQuestionPoints;

      if (questionResult.isCorrect) {
        consecutiveCorrect++;
        maxStreak = Math.max(maxStreak, consecutiveCorrect);
        totalDifficultyPoints += baseQuestionPoints;
        results.correctAnswers.push({
          ...question,
          userAnswer,
          points: baseQuestionPoints,
          ...questionResult
        });
      } else if (questionResult.isPartial) {
        consecutiveCorrect = 0;
        const partialPoints = baseQuestionPoints * questionResult.partialScore;
        totalDifficultyPoints += partialPoints;
        results.partialAnswers.push({
          ...question,
          userAnswer,
          points: partialPoints,
          ...questionResult
        });
      } else {
        consecutiveCorrect = 0;
        results.wrongAnswers.push({
          ...question,
          userAnswer,
          points: 0,
          ...questionResult
        });
      }
    }

    // Calculate base score
    results.baseScore = Math.round((totalDifficultyPoints / totalPossiblePoints) * 100);

    // Calculate bonuses
    results.breakdown.difficultyBonus = this.calculateDifficultyBonus(questions, results.correctAnswers);
    results.breakdown.timeBonus = this.calculateTimeBonus(timeSpent, totalTimeAllowed);
    results.breakdown.streakBonus = this.calculateStreakBonus(maxStreak);
    results.breakdown.consistencyBonus = await this.calculateConsistencyBonus(user._id);
    results.breakdown.improvementBonus = await this.calculateImprovementBonus(user._id, examData.subject);

    // Calculate total bonus points
    results.bonusPoints = Object.values(results.breakdown).reduce((sum, bonus) => sum + bonus, 0);

    // Calculate final score and points
    results.finalScore = Math.min(100, results.baseScore + results.bonusPoints);
    results.finalPoints = Math.round(totalDifficultyPoints + (results.bonusPoints * 2)); // Bonus points worth 2x in point calculation

    // Determine pass/fail
    const passingPercentage = examData.passingMarks || 70;
    results.verdict = results.finalScore >= passingPercentage ? "Pass" : "Fail";

    // Check for achievements
    results.achievements = await this.checkAchievements(user, results, maxStreak, examData);

    return results;
  }

  /**
   * Evaluate a single question
   */
  evaluateQuestion(question, userAnswer, gptMap) {
    const result = {
      isCorrect: false,
      isPartial: false,
      partialScore: 0,
      reason: "",
      confidence: 1.0
    };

    if (question.type === "fill" || question.answerType === "Free Text" || question.answerType === "Fill in the Blank") {
      const gptResult = gptMap[question.name] || {};
      
      if (gptResult.isCorrect) {
        result.isCorrect = true;
        result.confidence = gptResult.confidence || 1.0;
      } else if (gptResult.partialScore && gptResult.partialScore > 0) {
        result.isPartial = true;
        result.partialScore = gptResult.partialScore;
        result.confidence = gptResult.confidence || 0.5;
      }
      
      result.reason = gptResult.reason || "";
    } else if (question.type === "mcq" || question.answerType === "Options") {
      const correctKey = question.correctOption || question.correctAnswer;
      result.isCorrect = correctKey === userAnswer;
    }

    return result;
  }

  /**
   * Calculate difficulty bonus based on question difficulty distribution
   */
  calculateDifficultyBonus(questions, correctAnswers) {
    const hardQuestions = correctAnswers.filter(q => q.difficultyLevel === 'hard').length;
    const mediumQuestions = correctAnswers.filter(q => q.difficultyLevel === 'medium').length;
    
    return (hardQuestions * 3) + (mediumQuestions * 1); // 3 bonus points per hard, 1 per medium
  }

  /**
   * Calculate time bonus/penalty
   */
  calculateTimeBonus(timeSpent, totalTimeAllowed) {
    if (!timeSpent || !totalTimeAllowed) return 0;
    
    const timeRatio = timeSpent / totalTimeAllowed;
    
    if (timeRatio <= 0.5) {
      return 10; // Completed in half the time
    } else if (timeRatio <= 0.75) {
      return 5; // Completed in 3/4 the time
    } else if (timeRatio <= 1.0) {
      return 0; // Completed within time limit
    } else {
      return -5; // Overtime penalty
    }
  }

  /**
   * Calculate streak bonus
   */
  calculateStreakBonus(maxStreak) {
    if (maxStreak >= 10) return 15;
    if (maxStreak >= 7) return 10;
    if (maxStreak >= 5) return 5;
    if (maxStreak >= 3) return 2;
    return 0;
  }

  /**
   * Calculate consistency bonus based on recent performance
   */
  async calculateConsistencyBonus(userId) {
    try {
      const Report = require('../models/reportModel');
      
      // Get last 5 quiz results
      const recentReports = await Report.find({ user: userId })
        .sort({ createdAt: -1 })
        .limit(5)
        .populate('exam');

      if (recentReports.length < 3) return 0;

      const scores = recentReports.map(r => r.result.score || 0);
      const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
      const variance = scores.reduce((sum, score) => sum + Math.pow(score - average, 2), 0) / scores.length;
      const standardDeviation = Math.sqrt(variance);

      // Lower standard deviation = more consistent = higher bonus
      if (standardDeviation <= 5) return 8;
      if (standardDeviation <= 10) return 5;
      if (standardDeviation <= 15) return 2;
      return 0;
    } catch (error) {
      console.error('Error calculating consistency bonus:', error);
      return 0;
    }
  }

  /**
   * Calculate improvement bonus based on subject performance trend
   */
  async calculateImprovementBonus(userId, subject) {
    try {
      const Report = require('../models/reportModel');
      
      // Get last 3 attempts in this subject
      const subjectReports = await Report.find({ user: userId })
        .populate({
          path: 'exam',
          match: { subject: subject }
        })
        .sort({ createdAt: -1 })
        .limit(3);

      const validReports = subjectReports.filter(r => r.exam);
      
      if (validReports.length < 2) return 0;

      const scores = validReports.map(r => r.result.score || 0).reverse(); // Oldest first
      
      // Check for improvement trend
      let improvements = 0;
      for (let i = 1; i < scores.length; i++) {
        if (scores[i] > scores[i-1]) improvements++;
      }

      if (improvements === scores.length - 1) return 10; // Consistent improvement
      if (improvements >= Math.ceil((scores.length - 1) / 2)) return 5; // Mostly improving
      return 0;
    } catch (error) {
      console.error('Error calculating improvement bonus:', error);
      return 0;
    }
  }

  /**
   * Check for new achievements
   */
  async checkAchievements(user, results, maxStreak, examData) {
    const achievements = [];

    // Perfect score achievement
    if (results.finalScore === 100) {
      achievements.push({
        type: 'perfect_score',
        subject: examData.subject,
        metadata: { score: results.finalScore, exam: examData.name }
      });
    }

    // Streak achievements
    if (maxStreak >= 20) {
      achievements.push({ type: 'streak_20', metadata: { streak: maxStreak } });
    } else if (maxStreak >= 10) {
      achievements.push({ type: 'streak_10', metadata: { streak: maxStreak } });
    } else if (maxStreak >= 5) {
      achievements.push({ type: 'streak_5', metadata: { streak: maxStreak } });
    }

    // Speed achievement
    if (results.breakdown.timeBonus >= 10) {
      achievements.push({
        type: 'speed_demon',
        subject: examData.subject,
        metadata: { timeBonus: results.breakdown.timeBonus }
      });
    }

    // Consistency achievement
    if (results.breakdown.consistencyBonus >= 8) {
      achievements.push({
        type: 'consistent_learner',
        metadata: { consistencyBonus: results.breakdown.consistencyBonus }
      });
    }

    // Improvement achievement
    if (results.breakdown.improvementBonus >= 10) {
      achievements.push({
        type: 'improvement_star',
        subject: examData.subject,
        metadata: { improvementBonus: results.breakdown.improvementBonus }
      });
    }

    return achievements;
  }

  /**
   * Update user statistics after quiz completion
   */
  async updateUserStatistics(userId, results, examData) {
    try {
      const user = await User.findById(userId);
      if (!user) return;

      // Update basic stats
      user.totalQuizzesTaken = (user.totalQuizzesTaken || 0) + 1;
      user.totalPointsEarned = (user.totalPointsEarned || 0) + results.finalPoints;
      
      // Update average score
      const newAverage = ((user.averageScore || 0) * (user.totalQuizzesTaken - 1) + results.finalScore) / user.totalQuizzesTaken;
      user.averageScore = Math.round(newAverage * 100) / 100;

      // Update streak information
      if (results.verdict === 'Pass') {
        user.currentStreak = (user.currentStreak || 0) + 1;
        user.bestStreak = Math.max(user.bestStreak || 0, user.currentStreak);
      } else {
        user.currentStreak = 0;
      }

      // Add new achievements
      for (const achievement of results.achievements) {
        const existingAchievement = user.achievements.find(a => 
          a.type === achievement.type && 
          a.subject === achievement.subject
        );
        
        if (!existingAchievement) {
          user.achievements.push(achievement);
        }
      }

      await user.save();
    } catch (error) {
      console.error('Error updating user statistics:', error);
    }
  }
}

module.exports = new EnhancedQuizMarkingService();
