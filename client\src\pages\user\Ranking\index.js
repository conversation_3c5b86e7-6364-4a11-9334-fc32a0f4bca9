import React, { useEffect, useState, useRef } from "react";
import { useSelector } from "react-redux";
import { motion, AnimatePresence } from "framer-motion";
import { TbTrophy, TbMedal, TbRefresh, TbAlertCircle, TbArrowLeft, TbTarget, TbStar, TbFlame, TbAward } from "react-icons/tb";
import { getAllReportsForRanking } from "../../../apicalls/reports";
import UserRankingList from "../../../components/modern/UserRankingList";
import { message } from "antd";

const Ranking = () => {
    const userState = useSelector((state) => state.users || {});
    const { user } = userState;
    const [rankingData, setRankingData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [refreshing, setRefreshing] = useState(false);
    const [currentUserRank, setCurrentUserRank] = useState(null);
    const [showFindMe, setShowFindMe] = useState(false);
    const currentUserRef = useRef(null);

    const fetchRankingData = async (showRefreshMessage = false) => {
        try {
            if (showRefreshMessage) {
                setRefreshing(true);
                message.loading("Refreshing rankings...", 1);
            }

            // Try XP leaderboard first, then enhanced leaderboard, fallback to regular ranking
            let response;

            // Add cache-busting timestamp to ensure fresh data
            const timestamp = new Date().getTime();

            try {
                // Try new XP-based leaderboard first
                const xpResponse = await fetch(`/api/quiz/xp-leaderboard?limit=1000&t=${timestamp}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`,
                        'Cache-Control': 'no-cache'
                    }
                });
                const xpData = await xpResponse.json();

                if (xpData.success) {
                    response = xpData;
                    console.log('Using XP-based leaderboard');
                } else {
                    throw new Error('XP leaderboard failed, trying enhanced leaderboard');
                }
            } catch (xpError) {
                console.log('XP leaderboard failed, trying enhanced leaderboard:', xpError);
                try {
                    const enhancedResponse = await fetch(`/api/quiz/enhanced-leaderboard?limit=1000&t=${timestamp}`, {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('token')}`,
                            'Cache-Control': 'no-cache'
                        }
                    });
                    const enhancedData = await enhancedResponse.json();

                    if (enhancedData.success) {
                        response = enhancedData;
                        console.log('Using enhanced leaderboard');
                    } else {
                        throw new Error('Enhanced leaderboard failed');
                    }
                } catch (enhancedError) {
                    console.log('Falling back to regular ranking:', enhancedError);
                    response = await getAllReportsForRanking();
                }
            }

            if (response.success) {
                // Transform data to match UserRankingCard expectations with XP system support
                const transformedData = response.data.map((userData, index) => ({
                    userId: userData.userId || userData._id,
                    _id: userData.userId || userData._id,
                    name: userData.userName || userData.name,
                    profilePicture: userData.userPhoto || userData.profileImage,
                    school: userData.userSchool || userData.school,
                    class: userData.userClass || userData.class,
                    level: userData.userLevel || userData.level,

                    // Legacy points system
                    totalPoints: userData.totalPointsEarned || userData.totalPoints || 0,
                    quizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,
                    passedExamsCount: userData.passedExamsCount || 0,
                    retryCount: userData.retryCount || 0,
                    scoreRatio: userData.scoreRatio || 0,

                    // XP System data (new)
                    totalXP: userData.totalXP || 0,
                    currentLevel: userData.currentLevel || 1,
                    xpToNextLevel: userData.xpToNextLevel || 0,
                    seasonXP: userData.seasonXP || 0,
                    lifetimeXP: userData.lifetimeXP || 0,

                    // Statistics
                    averageScore: userData.averageScore || 0,
                    bestStreak: userData.bestStreak || 0,
                    currentStreak: userData.currentStreak || 0,
                    achievements: userData.achievements || [],

                    // Ranking data (prioritize XP-based ranking)
                    rankingScore: userData.rankingScore || userData.enhancedRankingScore || userData.totalXP || userData.totalPoints || 0,
                    rank: userData.rank || index + 1,
                    subscriptionStatus: userData.subscriptionStatus || 'free',

                    // XP breakdown (if available from new system)
                    breakdown: userData.breakdown || null
                }));

                setRankingData(transformedData);
                setError(null);

                // Find current user's rank
                const userRank = transformedData.findIndex(item =>
                    item._id === user?._id || item.userId === user?._id
                );
                setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);

                if (showRefreshMessage) {
                    message.success("Rankings updated successfully!");
                }
            } else {
                setError(response.message || "Failed to fetch ranking data");
                message.error("Failed to load rankings");
            }
        } catch (err) {
            console.error('Ranking fetch error:', err);
            setError(err.message || "An error occurred while fetching rankings");
            message.error("Network error while loading rankings");
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    useEffect(() => {
        fetchRankingData();
    }, []);

    // Find Me functionality
    const handleFindMe = () => {
        if (currentUserRef.current) {
            currentUserRef.current.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
            setShowFindMe(true);
            setTimeout(() => setShowFindMe(false), 3000); // Hide highlight after 3 seconds
        }
    };

    const handleRefresh = () => {
        fetchRankingData(true);
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
                <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="text-center bg-white rounded-xl p-8 shadow-lg"
                >
                    <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"
                    />
                    <p className="text-gray-600 font-medium">Loading rankings...</p>
                </motion.div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center bg-white rounded-xl p-8 shadow-lg max-w-md w-full"
                >
                    <TbAlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Rankings</h2>
                    <p className="text-gray-600 mb-6">{error}</p>
                    <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={handleRefresh}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 mx-auto"
                    >
                        <TbRefresh className="w-5 h-5" />
                        <span>Try Again</span>
                    </motion.button>
                </motion.div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 relative overflow-hidden">
            {/* Animated Background Elements */}
            <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-40 -right-40 w-80 h-80 bg-yellow-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
                <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
                <div className="absolute top-40 left-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
            </div>

            <div className="relative z-10">
                {/* Modern Header with Back Button */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="p-4 flex items-center justify-between"
                >
                    <motion.button
                        whileHover={{ scale: 1.05, x: -5 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => window.history.back()}
                        className="bg-white/10 backdrop-blur-lg hover:bg-white/20 text-white px-4 py-2 rounded-xl font-medium transition-all duration-200 flex items-center space-x-2 border border-white/20"
                    >
                        <TbArrowLeft className="w-5 h-5" />
                        <span>Back</span>
                    </motion.button>

                    {/* Find Me Button */}
                    {currentUserRank && (
                        <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={handleFindMe}
                            className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-6 py-2 rounded-xl font-bold transition-all duration-200 flex items-center space-x-2 shadow-lg"
                        >
                            <TbTarget className="w-5 h-5" />
                            <span>Find Me</span>
                        </motion.button>
                    )}
                </motion.div>

                {/* Amazing Header */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center mb-8 px-4"
                >
                    <div className="flex items-center justify-center gap-4 mb-6">
                        <motion.div
                            animate={{
                                rotate: [0, 10, -10, 0],
                                scale: [1, 1.1, 1]
                            }}
                            transition={{ duration: 3, repeat: Infinity, repeatDelay: 2 }}
                            className="relative"
                        >
                            <TbTrophy className="text-6xl text-yellow-400 drop-shadow-lg" />
                            <motion.div
                                animate={{ scale: [1, 1.2, 1] }}
                                transition={{ duration: 2, repeat: Infinity }}
                                className="absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full"
                            />
                        </motion.div>
                        <div>
                            <h1 className="text-5xl font-black bg-gradient-to-r from-yellow-400 via-pink-400 to-purple-400 bg-clip-text text-transparent mb-2">
                                🏆 LEADERBOARD
                            </h1>
                            <div className="flex items-center justify-center gap-2 text-white/80">
                                <TbStar className="text-yellow-400" />
                                <span className="text-lg font-medium">Battle for Glory</span>
                                <TbStar className="text-yellow-400" />
                            </div>
                        </div>
                    </div>

                    {/* Stats Bar */}
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.3 }}
                        className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 mb-6 border border-white/20 max-w-4xl mx-auto"
                    >
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                            <div className="flex items-center justify-center gap-2">
                                <TbFlame className="text-orange-400 text-xl" />
                                <span className="text-white font-semibold">
                                    {currentUserRank ? `Your Rank: #${currentUserRank}` : 'Join the Competition!'}
                                </span>
                            </div>
                            <div className="flex items-center justify-center gap-2">
                                <TbAward className="text-purple-400 text-xl" />
                                <span className="text-white font-semibold">
                                    {rankingData.length} Competitors
                                </span>
                            </div>
                            <div className="flex items-center justify-center gap-2">
                                <TbMedal className="text-yellow-400 text-xl" />
                                <span className="text-white font-semibold">
                                    Live Rankings
                                </span>
                            </div>
                        </div>
                    </motion.div>
                </motion.div>
            </div>

            {/* Main Content */}
            <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
                {rankingData.length === 0 ? (
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="text-center py-16 bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20"
                    >
                        <motion.div
                            animate={{ rotate: [0, 10, -10, 0] }}
                            transition={{ duration: 2, repeat: Infinity }}
                        >
                            <TbMedal className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
                        </motion.div>
                        <h3 className="text-2xl font-bold text-white mb-2">No Rankings Available</h3>
                        <p className="text-white/80 mb-6 text-lg">Complete some quizzes to join the leaderboard!</p>
                        <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={handleRefresh}
                            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-bold transition-all duration-200 shadow-lg"
                        >
                            <TbRefresh className="w-5 h-5 inline mr-2" />
                            Refresh Rankings
                        </motion.button>
                    </motion.div>
                ) : (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                        className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-6"
                    >
                        <UserRankingList
                            users={rankingData}
                            currentUserId={user?._id || null}
                            layout="horizontal"
                            size="medium"
                            showStats={true}
                            className="space-y-4"
                            currentUserRef={currentUserRef}
                            showFindMe={showFindMe}
                        />
                    </motion.div>
                )}
            </div>
        </div>
    );
};

export default Ranking;