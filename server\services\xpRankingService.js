const User = require("../models/userModel");
const XPTransaction = require("../models/xpTransactionModel");
const LevelDefinition = require("../models/levelDefinitionModel");
const Report = require("../models/reportModel");

class XPRankingService {
  constructor() {
    // Ranking weight configuration
    this.weights = {
      totalXP: 1.0,           // Base XP weight
      currentLevel: 100,      // Level bonus (100 XP per level)
      averageScore: 2.0,      // Average score multiplier
      streakBonus: 10,        // Streak bonus (10 XP per streak)
      achievementBonus: 25,   // Achievement bonus (25 XP per achievement)
      activityBonus: 0.5,     // Recent activity bonus
      premiumBonus: 50,       // Premium user bonus
    };

    // Seasonal ranking configuration
    this.seasonConfig = {
      currentSeason: "2024-S1",
      seasonWeight: 0.3,      // 30% weight for seasonal XP
      lifetimeWeight: 0.7,    // 70% weight for lifetime XP
    };
  }

  /**
   * Calculate comprehensive ranking score for a user
   */
  async calculateRankingScore(user) {
    try {
      // Base XP score
      const baseXPScore = (user.totalXP || 0) * this.weights.totalXP;
      
      // Level bonus
      const levelBonus = (user.currentLevel || 1) * this.weights.currentLevel;
      
      // Average score bonus
      const averageScoreBonus = (user.averageScore || 0) * this.weights.averageScore;
      
      // Streak bonus
      const streakBonus = (user.bestStreak || 0) * this.weights.streakBonus;
      
      // Achievement bonus
      const achievementCount = user.achievements ? user.achievements.length : 0;
      const achievementBonus = achievementCount * this.weights.achievementBonus;
      
      // Recent activity bonus (last 7 days)
      const activityBonus = await this.calculateActivityBonus(user._id);
      
      // Premium user bonus
      const premiumBonus = this.isPremiumUser(user) ? this.weights.premiumBonus : 0;
      
      // Seasonal vs Lifetime XP weighting
      const seasonalScore = (user.seasonXP || 0) * this.seasonConfig.seasonWeight;
      const lifetimeScore = (user.lifetimeXP || 0) * this.seasonConfig.lifetimeWeight;
      const weightedXPScore = seasonalScore + lifetimeScore;
      
      // Calculate final ranking score
      const rankingScore = Math.round(
        weightedXPScore +
        levelBonus +
        averageScoreBonus +
        streakBonus +
        achievementBonus +
        activityBonus +
        premiumBonus
      );

      return {
        rankingScore: rankingScore,
        breakdown: {
          baseXP: Math.round(baseXPScore),
          weightedXP: Math.round(weightedXPScore),
          levelBonus: Math.round(levelBonus),
          averageScoreBonus: Math.round(averageScoreBonus),
          streakBonus: Math.round(streakBonus),
          achievementBonus: Math.round(achievementBonus),
          activityBonus: Math.round(activityBonus),
          premiumBonus: Math.round(premiumBonus),
        }
      };

    } catch (error) {
      console.error('Error calculating ranking score:', error);
      return { rankingScore: 0, breakdown: {} };
    }
  }

  /**
   * Calculate recent activity bonus
   */
  async calculateActivityBonus(userId) {
    try {
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const recentTransactions = await XPTransaction.find({
        user: userId,
        createdAt: { $gte: sevenDaysAgo }
      });

      const recentXP = recentTransactions.reduce((sum, transaction) => 
        sum + transaction.xpAmount, 0
      );

      return recentXP * this.weights.activityBonus;

    } catch (error) {
      console.error('Error calculating activity bonus:', error);
      return 0;
    }
  }

  /**
   * Check if user is premium
   */
  isPremiumUser(user) {
    const premiumStatuses = ['premium', 'active'];
    const hasValidSubscription = premiumStatuses.includes(user.subscriptionStatus);
    
    if (!hasValidSubscription) return false;
    
    // Check subscription end date if it exists
    if (user.subscriptionEndDate) {
      return new Date(user.subscriptionEndDate) > new Date();
    }
    
    return true;
  }

  /**
   * Get comprehensive leaderboard with XP-based ranking
   */
  async getXPLeaderboard(options = {}) {
    try {
      const {
        limit = 100,
        classFilter = null,
        levelFilter = null,
        seasonFilter = null,
        includeInactive = false
      } = options;

      // Build match criteria
      const matchCriteria = {
        role: { $ne: 'admin' } // Exclude admins
      };

      if (classFilter) {
        matchCriteria.class = classFilter;
      }

      if (levelFilter) {
        matchCriteria.level = levelFilter;
      }

      if (seasonFilter) {
        matchCriteria.currentSeason = seasonFilter;
      }

      if (!includeInactive) {
        // Only include users with some activity
        matchCriteria.totalQuizzesTaken = { $gt: 0 };
      }

      // Aggregation pipeline for enhanced ranking
      const pipeline = [
        { $match: matchCriteria },
        
        // Calculate ranking score
        {
          $addFields: {
            // Base XP calculation
            baseXPScore: { $multiply: [{ $ifNull: ["$totalXP", 0] }, this.weights.totalXP] },
            
            // Level bonus
            levelBonus: { $multiply: [{ $ifNull: ["$currentLevel", 1] }, this.weights.currentLevel] },
            
            // Average score bonus
            averageScoreBonus: { $multiply: [{ $ifNull: ["$averageScore", 0] }, this.weights.averageScore] },
            
            // Streak bonus
            streakBonus: { $multiply: [{ $ifNull: ["$bestStreak", 0] }, this.weights.streakBonus] },
            
            // Achievement bonus
            achievementBonus: { 
              $multiply: [
                { $size: { $ifNull: ["$achievements", []] } },
                this.weights.achievementBonus
              ]
            },
            
            // Premium bonus
            premiumBonus: {
              $cond: [
                {
                  $and: [
                    { $in: ["$subscriptionStatus", ["premium", "active"]] },
                    {
                      $or: [
                        { $eq: ["$subscriptionEndDate", null] },
                        { $gte: ["$subscriptionEndDate", new Date()] }
                      ]
                    }
                  ]
                },
                this.weights.premiumBonus,
                0
              ]
            },
            
            // Weighted XP (seasonal + lifetime)
            weightedXP: {
              $add: [
                { $multiply: [{ $ifNull: ["$seasonXP", 0] }, this.seasonConfig.seasonWeight] },
                { $multiply: [{ $ifNull: ["$lifetimeXP", 0] }, this.seasonConfig.lifetimeWeight] }
              ]
            }
          }
        },
        
        // Calculate final ranking score
        {
          $addFields: {
            rankingScore: {
              $round: {
                $add: [
                  "$weightedXP",
                  "$levelBonus",
                  "$averageScoreBonus",
                  "$streakBonus",
                  "$achievementBonus",
                  "$premiumBonus"
                ]
              }
            }
          }
        },
        
        // Sort by ranking score
        { $sort: { rankingScore: -1, totalXP: -1, name: 1 } },
        
        // Limit results
        { $limit: limit },
        
        // Add rank field
        {
          $group: {
            _id: null,
            users: { $push: "$$ROOT" }
          }
        },
        {
          $unwind: {
            path: "$users",
            includeArrayIndex: "rank"
          }
        },
        {
          $addFields: {
            "users.rank": { $add: ["$rank", 1] }
          }
        },
        {
          $replaceRoot: { newRoot: "$users" }
        },
        
        // Project final fields
        {
          $project: {
            _id: 1,
            name: 1,
            class: 1,
            level: 1,
            school: 1,
            profileImage: 1,
            subscriptionStatus: 1,
            subscriptionEndDate: 1,
            subscriptionPlan: 1,
            
            // XP and Level Info
            totalXP: 1,
            currentLevel: 1,
            xpToNextLevel: 1,
            seasonXP: 1,
            lifetimeXP: 1,
            
            // Statistics
            totalQuizzesTaken: 1,
            averageScore: 1,
            bestStreak: 1,
            currentStreak: 1,
            achievements: 1,
            
            // Ranking Info
            rank: 1,
            rankingScore: 1,
            
            // Score Breakdown
            breakdown: {
              baseXP: "$baseXPScore",
              weightedXP: "$weightedXP",
              levelBonus: "$levelBonus",
              averageScoreBonus: "$averageScoreBonus",
              streakBonus: "$streakBonus",
              achievementBonus: "$achievementBonus",
              premiumBonus: "$premiumBonus"
            }
          }
        }
      ];

      const leaderboard = await User.aggregate(pipeline);
      
      return {
        success: true,
        data: leaderboard,
        metadata: {
          totalUsers: leaderboard.length,
          season: this.seasonConfig.currentSeason,
          weights: this.weights,
          generatedAt: new Date()
        }
      };

    } catch (error) {
      console.error('Error generating XP leaderboard:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * Get user's ranking position and nearby users
   */
  async getUserRankingPosition(userId, context = 5) {
    try {
      const leaderboard = await this.getXPLeaderboard({ limit: 10000 });
      
      if (!leaderboard.success) {
        throw new Error('Failed to generate leaderboard');
      }

      const userIndex = leaderboard.data.findIndex(user => 
        user._id.toString() === userId.toString()
      );

      if (userIndex === -1) {
        return {
          success: false,
          message: 'User not found in rankings'
        };
      }

      const userRank = userIndex + 1;
      const startIndex = Math.max(0, userIndex - context);
      const endIndex = Math.min(leaderboard.data.length, userIndex + context + 1);
      
      const nearbyUsers = leaderboard.data.slice(startIndex, endIndex);

      return {
        success: true,
        userRank: userRank,
        totalUsers: leaderboard.data.length,
        user: leaderboard.data[userIndex],
        nearbyUsers: nearbyUsers,
        context: {
          showingFrom: startIndex + 1,
          showingTo: endIndex,
          contextSize: context
        }
      };

    } catch (error) {
      console.error('Error getting user ranking position:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get class-specific rankings
   */
  async getClassRankings(className, limit = 50) {
    return this.getXPLeaderboard({
      limit: limit,
      classFilter: className
    });
  }

  /**
   * Get seasonal rankings
   */
  async getSeasonalRankings(season = null, limit = 100) {
    const targetSeason = season || this.seasonConfig.currentSeason;
    return this.getXPLeaderboard({
      limit: limit,
      seasonFilter: targetSeason
    });
  }
}

module.exports = new XPRankingService();
