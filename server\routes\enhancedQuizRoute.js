const router = require("express").Router();
const authMiddleware = require("../middlewares/authMiddleware");
const enhancedQuizMarkingService = require("../services/enhancedQuizMarkingService");
const xpCalculationService = require("../services/xpCalculationService");
const xpRankingService = require("../services/xpRankingService");

// Enhanced quiz scoring endpoint
router.post("/calculate-enhanced-score", authMiddleware, async (req, res) => {
  try {
    const {
      questions,
      selectedOptions,
      gptMap,
      timeSpent,
      totalTimeAllowed,
      user,
      examData
    } = req.body;

    // Validate required parameters
    if (!questions || !selectedOptions || !user || !examData) {
      return res.status(400).send({
        message: "Missing required parameters",
        success: false,
      });
    }

    // Calculate enhanced score
    const result = await enhancedQuizMarkingService.calculateEnhancedScore({
      questions,
      selectedOptions,
      gptMap: gptMap || {},
      timeSpent,
      totalTimeAllowed,
      user,
      examData
    });

    // Update user statistics (legacy system)
    await enhancedQuizMarkingService.updateUserStatistics(user._id, result, examData);

    // NEW XP SYSTEM: Award XP based on enhanced scoring
    try {
      const xpResult = await xpCalculationService.calculateQuizXP({
        user: user,
        examData: examData,
        questions: questions,
        correctAnswers: result.correctAnswers || [],
        wrongAnswers: result.wrongAnswers || [],
        timeSpent: timeSpent,
        totalTimeAllowed: totalTimeAllowed,
        isFirstAttempt: true, // Enhanced scoring typically used for first attempts
        difficulty: examData.difficulty || examData.difficultyLevel || 'medium'
      });

      const xpAwardResult = await xpCalculationService.awardXP({
        userId: user._id,
        xpAmount: xpResult.xpAwarded,
        transactionType: 'quiz_completion',
        sourceId: examData._id,
        sourceModel: 'exams',
        breakdown: xpResult.breakdown,
        quizData: {
          examId: examData._id,
          subject: examData.subject,
          difficulty: examData.difficulty || examData.difficultyLevel || 'medium',
          questionsTotal: questions.length,
          questionsCorrect: result.correctAnswers?.length || 0,
          timeSpent: timeSpent,
          score: result.finalScore || result.baseScore || 0,
          isFirstAttempt: true,
        },
        metadata: {
          enhancedScoring: true,
          bonusPoints: result.bonusPoints || 0,
          achievements: result.achievements || [],
          ...xpResult.metadata
        }
      });

      // Add XP information to response
      result.xpAwarded = xpResult.xpAwarded;
      result.xpBreakdown = xpResult.breakdown;
      result.levelUp = xpAwardResult.levelUp;
      result.newLevel = xpAwardResult.newLevel;
      result.newTotalXP = xpAwardResult.newTotalXP;

    } catch (xpError) {
      console.error('Error awarding XP in enhanced quiz:', xpError);
      // Don't fail the entire request if XP calculation fails
    }

    res.send({
      message: "Enhanced score calculated successfully",
      data: result,
      success: true,
    });
  } catch (error) {
    console.error("Enhanced scoring error:", error);
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// Get user achievements
router.get("/achievements/:userId", authMiddleware, async (req, res) => {
  try {
    const { userId } = req.params;
    const User = require("../models/userModel");
    
    const user = await User.findById(userId).select('achievements totalQuizzesTaken totalPointsEarned averageScore bestStreak currentStreak');
    
    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    // Group achievements by type
    const achievementsByType = {};
    user.achievements.forEach(achievement => {
      if (!achievementsByType[achievement.type]) {
        achievementsByType[achievement.type] = [];
      }
      achievementsByType[achievement.type].push(achievement);
    });

    res.send({
      message: "Achievements fetched successfully",
      data: {
        achievements: user.achievements,
        achievementsByType,
        stats: {
          totalQuizzesTaken: user.totalQuizzesTaken || 0,
          totalPointsEarned: user.totalPointsEarned || 0,
          averageScore: user.averageScore || 0,
          bestStreak: user.bestStreak || 0,
          currentStreak: user.currentStreak || 0
        }
      },
      success: true,
    });
  } catch (error) {
    console.error("Get achievements error:", error);
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// Get user performance analytics
router.get("/analytics/:userId", authMiddleware, async (req, res) => {
  try {
    const { userId } = req.params;
    const { timeframe = '30d' } = req.query; // 7d, 30d, 90d, all
    
    const Report = require("../models/reportModel");
    const User = require("../models/userModel");
    
    // Calculate date filter
    let dateFilter = {};
    if (timeframe !== 'all') {
      const days = parseInt(timeframe.replace('d', ''));
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      dateFilter = { createdAt: { $gte: startDate } };
    }

    // Get user reports
    const reports = await Report.find({ 
      user: userId,
      ...dateFilter
    })
    .populate('exam', 'subject name level class')
    .sort({ createdAt: 1 });

    // Get user data
    const user = await User.findById(userId).select('achievements totalQuizzesTaken totalPointsEarned averageScore bestStreak currentStreak');

    // Calculate analytics
    const analytics = {
      totalQuizzes: reports.length,
      averageScore: 0,
      totalPoints: 0,
      passRate: 0,
      subjectPerformance: {},
      scoreProgression: [],
      difficultyAnalysis: {
        easy: { attempted: 0, passed: 0 },
        medium: { attempted: 0, passed: 0 },
        hard: { attempted: 0, passed: 0 }
      },
      recentAchievements: user.achievements.slice(-5),
      streakData: {
        current: user.currentStreak || 0,
        best: user.bestStreak || 0
      }
    };

    if (reports.length > 0) {
      const scores = reports.map(r => r.result.score || 0);
      const passedQuizzes = reports.filter(r => r.result.verdict === 'Pass').length;
      
      analytics.averageScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
      analytics.totalPoints = reports.reduce((sum, r) => sum + (r.result.points || 0), 0);
      analytics.passRate = Math.round((passedQuizzes / reports.length) * 100);

      // Subject performance
      reports.forEach(report => {
        const subject = report.exam?.subject || 'Unknown';
        if (!analytics.subjectPerformance[subject]) {
          analytics.subjectPerformance[subject] = {
            attempted: 0,
            passed: 0,
            averageScore: 0,
            totalScore: 0
          };
        }
        
        analytics.subjectPerformance[subject].attempted++;
        analytics.subjectPerformance[subject].totalScore += report.result.score || 0;
        
        if (report.result.verdict === 'Pass') {
          analytics.subjectPerformance[subject].passed++;
        }
      });

      // Calculate average scores for subjects
      Object.keys(analytics.subjectPerformance).forEach(subject => {
        const subjectData = analytics.subjectPerformance[subject];
        subjectData.averageScore = Math.round(subjectData.totalScore / subjectData.attempted);
        subjectData.passRate = Math.round((subjectData.passed / subjectData.attempted) * 100);
      });

      // Score progression (last 10 quizzes)
      analytics.scoreProgression = reports.slice(-10).map((report, index) => ({
        quiz: index + 1,
        score: report.result.score || 0,
        subject: report.exam?.subject || 'Unknown',
        date: report.createdAt
      }));
    }

    res.send({
      message: "Analytics fetched successfully",
      data: analytics,
      success: true,
    });
  } catch (error) {
    console.error("Get analytics error:", error);
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// Get comprehensive leaderboard with ALL users and calculated points
router.get("/enhanced-leaderboard", authMiddleware, async (req, res) => {
  try {
    const { level, limit = 1000 } = req.query;
    const User = require("../models/userModel");
    const Report = require("../models/reportModel");

    // Build match conditions - EXCLUDE ADMINS from rankings
    let matchConditions = {
      isAdmin: { $ne: true }, // Exclude admin users
      isBlocked: { $ne: true } // Also exclude blocked users
    };

    if (level && level !== 'all') {
      matchConditions.level = level;
    }

    // Get all users and calculate their actual points from reports
    const leaderboard = await User.aggregate([
      { $match: matchConditions },
      {
        $lookup: {
          from: "reports",
          localField: "_id",
          foreignField: "user",
          as: "reports"
        }
      },
      {
        $addFields: {
          // Calculate actual points from reports
          calculatedTotalPoints: {
            $sum: {
              $map: {
                input: "$reports",
                as: "report",
                in: { $ifNull: ["$$report.result.points", 0] }
              }
            }
          },
          // Calculate actual average score from reports
          calculatedAverageScore: {
            $cond: [
              { $gt: [{ $size: "$reports" }, 0] },
              {
                $avg: {
                  $map: {
                    input: "$reports",
                    as: "report",
                    in: { $ifNull: ["$$report.result.score", 0] }
                  }
                }
              },
              0
            ]
          },
          // Calculate actual quizzes taken
          calculatedQuizzesTaken: { $size: "$reports" },
          // Calculate pass rate
          passRate: {
            $cond: [
              { $gt: [{ $size: "$reports" }, 0] },
              {
                $multiply: [
                  {
                    $divide: [
                      {
                        $size: {
                          $filter: {
                            input: "$reports",
                            as: "report",
                            cond: { $eq: ["$$report.result.verdict", "Pass"] }
                          }
                        }
                      },
                      { $size: "$reports" }
                    ]
                  },
                  100
                ]
              },
              0
            ]
          }
        }
      },
      {
        $project: {
          name: 1,
          school: 1,
          class: 1,
          level: 1,
          profileImage: 1,
          subscriptionStatus: 1,
          subscriptionEndDate: 1,
          // Use calculated values instead of stored values
          totalQuizzesTaken: "$calculatedQuizzesTaken",
          totalPointsEarned: "$calculatedTotalPoints",
          averageScore: { $round: ["$calculatedAverageScore", 1] },
          passRate: { $round: ["$passRate", 1] },
          bestStreak: { $ifNull: ["$bestStreak", 0] },
          currentStreak: { $ifNull: ["$currentStreak", 0] },
          achievements: { $ifNull: ["$achievements", []] },
          isAdmin: 1,
          // Calculate actual subscription status
          actualSubscriptionStatus: {
            $cond: [
              {
                $and: [
                  { $in: ["$subscriptionStatus", ["premium", "active"]] },
                  {
                    $or: [
                      { $eq: ["$subscriptionEndDate", null] },
                      { $gte: ["$subscriptionEndDate", new Date()] }
                    ]
                  }
                ]
              },
              "premium",
              {
                $cond: [
                  {
                    $and: [
                      { $ne: ["$subscriptionEndDate", null] },
                      { $lt: ["$subscriptionEndDate", new Date()] }
                    ]
                  },
                  "expired",
                  "free"
                ]
              }
            ]
          },
          // Calculate enhanced ranking score using calculated values
          enhancedRankingScore: {
            $add: [
              { $multiply: ["$calculatedTotalPoints", 1] }, // Base points from actual reports
              { $multiply: ["$calculatedAverageScore", 2] }, // Average score weight
              { $multiply: [{ $ifNull: ["$bestStreak", 0] }, 5] }, // Streak bonus
              { $multiply: [{ $size: { $ifNull: ["$achievements", []] } }, 10] }, // Achievement bonus
              { $multiply: ["$passRate", 0.5] }, // Pass rate bonus
              {
                $cond: [
                  {
                    $and: [
                      { $in: ["$subscriptionStatus", ["premium", "active"]] },
                      {
                        $or: [
                          { $eq: ["$subscriptionEndDate", null] },
                          { $gte: ["$subscriptionEndDate", new Date()] }
                        ]
                      }
                    ]
                  },
                  50, // Premium bonus only for active premium users
                  0
                ]
              }
            ]
          }
        }
      },
      { $sort: { enhancedRankingScore: -1, totalPointsEarned: -1, averageScore: -1, name: 1 } },
      { $limit: parseInt(limit) }
    ]);

    // Add rank to each user and update subscription status
    const rankedLeaderboard = leaderboard.map((user, index) => ({
      ...user,
      rank: index + 1,
      subscriptionStatus: user.actualSubscriptionStatus // Use calculated status
    }));

    res.send({
      message: "Enhanced leaderboard fetched successfully",
      data: rankedLeaderboard,
      success: true,
    });
  } catch (error) {
    console.error("Enhanced leaderboard error:", error);
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// XP-based leaderboard endpoint
router.get("/xp-leaderboard", authMiddleware, async (req, res) => {
  try {
    const {
      limit = 100,
      classFilter = null,
      levelFilter = null,
      seasonFilter = null,
      includeInactive = false
    } = req.query;

    const options = {
      limit: parseInt(limit),
      classFilter: classFilter,
      levelFilter: levelFilter,
      seasonFilter: seasonFilter,
      includeInactive: includeInactive === 'true'
    };

    const leaderboard = await xpRankingService.getXPLeaderboard(options);

    if (!leaderboard.success) {
      return res.status(500).send({
        message: "Failed to generate XP leaderboard",
        success: false,
        error: leaderboard.error
      });
    }

    res.send({
      message: "XP leaderboard generated successfully",
      success: true,
      data: leaderboard.data,
      metadata: leaderboard.metadata
    });

  } catch (error) {
    console.error("XP leaderboard error:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get user's ranking position
router.get("/user-ranking/:userId", authMiddleware, async (req, res) => {
  try {
    const { userId } = req.params;
    const { context = 5 } = req.query;

    const ranking = await xpRankingService.getUserRankingPosition(
      userId,
      parseInt(context)
    );

    if (!ranking.success) {
      return res.status(404).send({
        message: ranking.message || "User ranking not found",
        success: false,
        error: ranking.error
      });
    }

    res.send({
      message: "User ranking retrieved successfully",
      success: true,
      data: ranking
    });

  } catch (error) {
    console.error("User ranking error:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get class rankings
router.get("/class-rankings/:className", authMiddleware, async (req, res) => {
  try {
    const { className } = req.params;
    const { limit = 50 } = req.query;

    const rankings = await xpRankingService.getClassRankings(
      className,
      parseInt(limit)
    );

    if (!rankings.success) {
      return res.status(500).send({
        message: "Failed to generate class rankings",
        success: false,
        error: rankings.error
      });
    }

    res.send({
      message: "Class rankings generated successfully",
      success: true,
      data: rankings.data,
      metadata: rankings.metadata
    });

  } catch (error) {
    console.error("Class rankings error:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

module.exports = router;
