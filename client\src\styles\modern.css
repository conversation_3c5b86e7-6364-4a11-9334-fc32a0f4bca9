/* ===== BRAINWAVE MODERN DESIGN SYSTEM ===== */
/* Professional Educational Platform Styling with Blue Theme */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* ===== CSS RESET & BASE STYLES ===== */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif !important;
  line-height: 1.6 !important;
  color: #1f2937 !important;
  background-color: #ffffff !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  margin: 0 !important;
  padding: 0 !important;
}

/* ===== CSS CUSTOM PROPERTIES - BLUE THEME ===== */
:root {
  /* Primary Blue Colors - User Preferred */
  --primary: #007BFF;
  --primary-dark: #0056D2;
  --primary-light: #3b82f6;
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Semantic Colors - Enhanced for Educational Platform */
  --success: #10b981;
  --success-light: #d1fae5;
  --success-dark: #059669;
  --warning: #f59e0b;
  --warning-light: #fef3c7;
  --warning-dark: #d97706;
  --danger: #ef4444;
  --danger-light: #fee2e2;
  --danger-dark: #dc2626;
  --info: #06b6d4;
  --info-light: #cffafe;
  --info-dark: #0891b2;

  /* Neutral Colors - Optimized for Readability */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Spacing - Optimized for User Preference (Small Gaps) */
  --space-1: 0.25rem;    /* 4px */
  --space-2: 0.5rem;     /* 8px */
  --space-3: 0.75rem;    /* 12px */
  --space-4: 1rem;       /* 16px */
  --space-5: 1.25rem;    /* 20px */
  --space-6: 1.5rem;     /* 24px */
  --space-8: 2rem;       /* 32px */
  --space-10: 2.5rem;    /* 40px */
  --space-12: 3rem;      /* 48px */
  --space-16: 4rem;      /* 64px */
  --space-20: 5rem;      /* 80px */

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Enhanced Shadows with Blue Tint */
  --shadow-sm: 0 1px 2px 0 rgba(0, 123, 255, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 123, 255, 0.1), 0 2px 4px -1px rgba(0, 123, 255, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 123, 255, 0.1), 0 4px 6px -2px rgba(0, 123, 255, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 123, 255, 0.15), 0 10px 10px -5px rgba(0, 123, 255, 0.08);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 123, 255, 0.25);

  /* Glassmorphism Effects */
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(0, 123, 255, 0.1);
  --glass-blur: blur(20px);

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --shadow-blue: 0 4px 14px 0 rgba(59, 130, 246, 0.15);
  --shadow-primary: 0 4px 14px 0 rgba(0, 123, 255, 0.15);

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ===== UTILITY CLASSES ===== */

/* Display */
.d-flex { display: flex !important; }
.d-block { display: block !important; }
.d-inline-block { display: inline-block !important; }
.d-none { display: none !important; }
.d-grid { display: grid !important; }

/* Flexbox */
.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-start { justify-content: flex-start !important; }
.justify-end { justify-content: flex-end !important; }
.align-center { align-items: center !important; }
.align-start { align-items: flex-start !important; }
.align-end { align-items: flex-end !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-1 { flex: 1 !important; }

/* Grid */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
.gap-1 { gap: var(--space-1) !important; }
.gap-2 { gap: var(--space-2) !important; }
.gap-3 { gap: var(--space-3) !important; }
.gap-4 { gap: var(--space-4) !important; }
.gap-6 { gap: var(--space-6) !important; }
.gap-8 { gap: var(--space-8) !important; }

/* Spacing - Margin */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--space-1) !important; }
.m-2 { margin: var(--space-2) !important; }
.m-3 { margin: var(--space-3) !important; }
.m-4 { margin: var(--space-4) !important; }
.m-5 { margin: var(--space-5) !important; }
.m-6 { margin: var(--space-6) !important; }
.m-8 { margin: var(--space-8) !important; }
.m-auto { margin: auto !important; }

.mx-auto { margin-left: auto !important; margin-right: auto !important; }
.my-auto { margin-top: auto !important; margin-bottom: auto !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--space-1) !important; }
.mt-2 { margin-top: var(--space-2) !important; }
.mt-3 { margin-top: var(--space-3) !important; }
.mt-4 { margin-top: var(--space-4) !important; }
.mt-6 { margin-top: var(--space-6) !important; }
.mt-8 { margin-top: var(--space-8) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--space-1) !important; }
.mb-2 { margin-bottom: var(--space-2) !important; }
.mb-3 { margin-bottom: var(--space-3) !important; }
.mb-4 { margin-bottom: var(--space-4) !important; }
.mb-6 { margin-bottom: var(--space-6) !important; }
.mb-8 { margin-bottom: var(--space-8) !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: var(--space-1) !important; }
.ml-2 { margin-left: var(--space-2) !important; }
.ml-3 { margin-left: var(--space-3) !important; }
.ml-4 { margin-left: var(--space-4) !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: var(--space-1) !important; }
.mr-2 { margin-right: var(--space-2) !important; }
.mr-3 { margin-right: var(--space-3) !important; }
.mr-4 { margin-right: var(--space-4) !important; }

/* Spacing - Padding */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--space-1) !important; }
.p-2 { padding: var(--space-2) !important; }
.p-3 { padding: var(--space-3) !important; }
.p-4 { padding: var(--space-4) !important; }
.p-5 { padding: var(--space-5) !important; }
.p-6 { padding: var(--space-6) !important; }
.p-8 { padding: var(--space-8) !important; }

.px-1 { padding-left: var(--space-1) !important; padding-right: var(--space-1) !important; }
.px-2 { padding-left: var(--space-2) !important; padding-right: var(--space-2) !important; }
.px-3 { padding-left: var(--space-3) !important; padding-right: var(--space-3) !important; }
.px-4 { padding-left: var(--space-4) !important; padding-right: var(--space-4) !important; }
.px-6 { padding-left: var(--space-6) !important; padding-right: var(--space-6) !important; }
.px-8 { padding-left: var(--space-8) !important; padding-right: var(--space-8) !important; }

.py-1 { padding-top: var(--space-1) !important; padding-bottom: var(--space-1) !important; }
.py-2 { padding-top: var(--space-2) !important; padding-bottom: var(--space-2) !important; }
.py-3 { padding-top: var(--space-3) !important; padding-bottom: var(--space-3) !important; }
.py-4 { padding-top: var(--space-4) !important; padding-bottom: var(--space-4) !important; }
.py-6 { padding-top: var(--space-6) !important; padding-bottom: var(--space-6) !important; }
.py-8 { padding-top: var(--space-8) !important; padding-bottom: var(--space-8) !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: var(--space-1) !important; }
.pt-2 { padding-top: var(--space-2) !important; }
.pt-3 { padding-top: var(--space-3) !important; }
.pt-4 { padding-top: var(--space-4) !important; }
.pt-6 { padding-top: var(--space-6) !important; }
.pt-8 { padding-top: var(--space-8) !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: var(--space-1) !important; }
.pb-2 { padding-bottom: var(--space-2) !important; }
.pb-3 { padding-bottom: var(--space-3) !important; }
.pb-4 { padding-bottom: var(--space-4) !important; }
.pb-6 { padding-bottom: var(--space-6) !important; }
.pb-8 { padding-bottom: var(--space-8) !important; }

/* Width & Height */
.w-full { width: 100% !important; }
.w-auto { width: auto !important; }
.w-fit { width: fit-content !important; }
.h-full { height: 100% !important; }
.h-auto { height: auto !important; }
.h-fit { height: fit-content !important; }
.h-screen { height: 100vh !important; }
.min-h-screen { min-height: 100vh !important; }

/* Position */
.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed { position: fixed !important; }
.sticky { position: sticky !important; }

/* Text Alignment */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

/* Font Weight */
.font-light { font-weight: 300 !important; }
.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }
.font-extrabold { font-weight: 800 !important; }

/* Font Size */
.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }
.text-3xl { font-size: var(--font-size-3xl) !important; }
.text-4xl { font-size: var(--font-size-4xl) !important; }

/* Line Height */
.leading-tight { line-height: var(--leading-tight) !important; }
.leading-normal { line-height: var(--leading-normal) !important; }
.leading-relaxed { line-height: var(--leading-relaxed) !important; }
.leading-loose { line-height: var(--leading-loose) !important; }

/* Colors - Text */
.text-white { color: var(--white) !important; }
.text-primary { color: var(--primary) !important; }
.text-primary-dark { color: var(--primary-dark) !important; }
.text-success { color: var(--success) !important; }
.text-warning { color: var(--warning) !important; }
.text-danger { color: var(--danger) !important; }
.text-info { color: var(--info) !important; }
.text-gray-400 { color: var(--gray-400) !important; }
.text-gray-500 { color: var(--gray-500) !important; }
.text-gray-600 { color: var(--gray-600) !important; }
.text-gray-700 { color: var(--gray-700) !important; }
.text-gray-800 { color: var(--gray-800) !important; }
.text-gray-900 { color: var(--gray-900) !important; }

/* Colors - Background */
.bg-white { background-color: var(--white) !important; }
.bg-primary { background-color: var(--primary) !important; }
.bg-primary-dark { background-color: var(--primary-dark) !important; }
.bg-primary-light { background-color: var(--primary-light) !important; }
.bg-primary-50 { background-color: var(--primary-50) !important; }
.bg-primary-100 { background-color: var(--primary-100) !important; }
.bg-success { background-color: var(--success) !important; }
.bg-success-light { background-color: var(--success-light) !important; }
.bg-warning { background-color: var(--warning) !important; }
.bg-warning-light { background-color: var(--warning-light) !important; }
.bg-danger { background-color: var(--danger) !important; }
.bg-danger-light { background-color: var(--danger-light) !important; }
.bg-info { background-color: var(--info) !important; }
.bg-info-light { background-color: var(--info-light) !important; }
.bg-gray-50 { background-color: var(--gray-50) !important; }
.bg-gray-100 { background-color: var(--gray-100) !important; }
.bg-gray-200 { background-color: var(--gray-200) !important; }
.bg-gray-300 { background-color: var(--gray-300) !important; }

/* Border Radius */
.rounded-none { border-radius: 0 !important; }
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded { border-radius: var(--radius-md) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-2xl { border-radius: var(--radius-2xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

/* Borders */
.border { border: 1px solid var(--gray-200) !important; }
.border-0 { border: 0 !important; }
.border-2 { border: 2px solid var(--gray-200) !important; }
.border-primary { border-color: var(--primary) !important; }
.border-success { border-color: var(--success) !important; }
.border-warning { border-color: var(--warning) !important; }
.border-danger { border-color: var(--danger) !important; }
.border-gray-200 { border-color: var(--gray-200) !important; }
.border-gray-300 { border-color: var(--gray-300) !important; }

/* Shadows */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-blue { box-shadow: var(--shadow-blue) !important; }
.shadow-primary { box-shadow: var(--shadow-primary) !important; }

/* Transitions */
.transition { transition: var(--transition-normal) !important; }
.transition-fast { transition: var(--transition-fast) !important; }
.transition-slow { transition: var(--transition-slow) !important; }
.transition-all { transition: all var(--transition-normal) !important; }
.transition-colors { transition: color var(--transition-normal), background-color var(--transition-normal), border-color var(--transition-normal) !important; }

/* Transform */
.transform { transform: translateZ(0) !important; }
.hover\:scale-105:hover { transform: scale(1.05) !important; }
.hover\:scale-110:hover { transform: scale(1.1) !important; }
.hover\:-translate-y-1:hover { transform: translateY(-0.25rem) !important; }
.hover\:-translate-y-2:hover { transform: translateY(-0.5rem) !important; }

/* Opacity */
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

/* Overflow */
.overflow-hidden { overflow: hidden !important; }
.overflow-auto { overflow: auto !important; }
.overflow-scroll { overflow: scroll !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-y-hidden { overflow-y: hidden !important; }
.overflow-x-auto { overflow-x: auto !important; }
.overflow-y-auto { overflow-y: auto !important; }

/* Z-Index */
.z-0 { z-index: 0 !important; }
.z-10 { z-index: 10 !important; }
.z-20 { z-index: 20 !important; }
.z-30 { z-index: 30 !important; }
.z-40 { z-index: 40 !important; }
.z-50 { z-index: 50 !important; }

/* ===== COMPONENT STYLES ===== */

/* Modern Card Component */
.card {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-100);
  transition: var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-100);
  background: var(--gray-50);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-100);
  background: var(--gray-50);
}

/* Glass Effect Card */
.card-glass {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
}

/* Modern Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: var(--font-size-sm);
  line-height: 1;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-base);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: var(--white);
  box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px -5px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-200);
}

.btn-secondary:hover {
  background: var(--gray-200);
  transform: translateY(-1px);
}

.btn-success {
  background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
  color: var(--white);
  box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.15);
}

.btn-success:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px -5px rgba(16, 185, 129, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
  color: var(--white);
  box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.15);
}

.btn-danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px -5px rgba(239, 68, 68, 0.3);
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--primary);
  color: var(--primary);
}

.btn-outline:hover {
  background: var(--primary);
  color: var(--white);
  transform: translateY(-1px);
}

/* Modern Form Elements - Blue Theme */
.form-group {
  margin-bottom: var(--space-4);
}

.form-label {
  display: block;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
  font-size: var(--font-size-sm);
}

.form-control,
.form-input,
.input-modern {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  background-color: var(--white);
  transition: var(--transition-normal);
  outline: none;
  font-family: inherit;
}

.form-control:focus,
.form-input:focus,
.input-modern:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  background-color: var(--white);
}

.form-control:disabled,
.form-input:disabled,
.input-modern:disabled {
  background-color: var(--gray-50);
  color: var(--gray-500);
  cursor: not-allowed;
}

.form-control.is-invalid,
.form-input.is-invalid,
.input-modern.is-invalid {
  border-color: var(--danger);
}

.form-control.is-invalid:focus,
.form-input.is-invalid:focus,
.input-modern.is-invalid:focus {
  border-color: var(--danger);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-control.is-valid,
.form-input.is-valid,
.input-modern.is-valid {
  border-color: var(--success);
}

.form-control.is-valid:focus,
.form-input.is-valid:focus,
.input-modern.is-valid:focus {
  border-color: var(--success);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-text {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  margin-top: var(--space-1);
}

.form-error {
  font-size: var(--font-size-xs);
  color: var(--danger);
  margin-top: var(--space-1);
}

.form-success {
  font-size: var(--font-size-xs);
  color: var(--success);
  margin-top: var(--space-1);
}

/* Select Dropdown */
.form-select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  background-color: var(--white);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--space-3) center;
  background-repeat: no-repeat;
  background-size: 1rem;
  padding-right: 2.5rem;
  transition: var(--transition-normal);
  outline: none;
  cursor: pointer;
}

.form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Checkbox and Radio */
.form-check {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
}

.form-check-input {
  width: 1.125rem;
  height: 1.125rem;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-sm);
  background-color: var(--white);
  cursor: pointer;
  transition: var(--transition-normal);
}

.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.form-check-input[type="radio"] {
  border-radius: var(--radius-full);
}

.form-check-label {
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  cursor: pointer;
}

/* Modern Layout Components */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(-1 * var(--space-3));
}

.col {
  flex: 1;
  padding: 0 var(--space-3);
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* Modern Navigation */
.navbar {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  padding: var(--space-4) 0;
}

.navbar-brand {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary);
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-link {
  color: var(--gray-600);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-normal);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary);
  background: var(--primary-50);
}

/* Modern Sidebar */
.sidebar {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: var(--white);
  min-height: 100vh;
  padding: var(--space-6);
  box-shadow: var(--shadow-xl);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-brand img {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
}

.sidebar-brand h3 {
  color: var(--white);
  font-weight: 700;
  margin: 0;
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu-item {
  margin-bottom: var(--space-2);
}

.sidebar-menu-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: var(--transition-normal);
  font-weight: 500;
}

.sidebar-menu-link:hover,
.sidebar-menu-link.active {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  transform: translateX(4px);
}

.sidebar-menu-icon {
  width: 20px;
  height: 20px;
  opacity: 0.8;
}

/* Modern Table */
.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.table th {
  background: var(--gray-50);
  padding: var(--space-4);
  text-align: left;
  font-weight: 600;
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
}

.table td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--gray-100);
  color: var(--gray-600);
}

.table tbody tr:hover {
  background: var(--gray-50);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* Modern Alert/Notification */
.alert {
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border: 1px solid transparent;
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.alert-success {
  background: var(--success-light);
  border-color: var(--success);
  color: #065f46;
}

.alert-warning {
  background: var(--warning-light);
  border-color: var(--warning);
  color: #92400e;
}

.alert-danger {
  background: var(--danger-light);
  border-color: var(--danger);
  color: #991b1b;
}

.alert-info {
  background: var(--info-light);
  border-color: var(--info);
  color: #155e75;
}

/* Modern Badge */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--font-size-xs);
  font-weight: 600;
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge-primary {
  background: var(--primary-100);
  color: var(--primary-800);
}

.badge-success {
  background: var(--success-light);
  color: #065f46;
}

.badge-warning {
  background: var(--warning-light);
  color: #92400e;
}

.badge-danger {
  background: var(--danger-light);
  color: #991b1b;
}

.badge-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
}

/* Modern Progress Bar */
.progress {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
}

.progress-bar-success {
  background: linear-gradient(90deg, var(--success) 0%, #10b981 100%);
}

.progress-bar-warning {
  background: linear-gradient(90deg, var(--warning) 0%, #f59e0b 100%);
}

.progress-bar-danger {
  background: linear-gradient(90deg, var(--danger) 0%, #ef4444 100%);
}

/* Modern Modal */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: var(--z-modal-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
}

.modal {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  z-index: var(--z-modal);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
}

.modal-close:hover {
  color: var(--gray-600);
  background: var(--gray-100);
}

.modal-body {
  padding: var(--space-6);
}

.modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

/* Modern Loading Spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

.spinner-sm {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.spinner-lg {
  width: 60px;
  height: 60px;
  border-width: 6px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modern Tooltip */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-text {
  visibility: hidden;
  width: 120px;
  background: var(--gray-900);
  color: var(--white);
  text-align: center;
  border-radius: var(--radius-md);
  padding: var(--space-2);
  position: absolute;
  z-index: var(--z-tooltip);
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity var(--transition-normal);
  font-size: var(--font-size-xs);
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Modern Dropdown */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  min-width: 200px;
  z-index: var(--z-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: var(--transition-normal);
}

.dropdown.active .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  padding: var(--space-3) var(--space-4);
  color: var(--gray-700);
  text-decoration: none;
  transition: var(--transition-normal);
  border-bottom: 1px solid var(--gray-100);
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: var(--gray-50);
  color: var(--primary);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile First Approach */
@media (max-width: 640px) {
  .container {
    padding: 0 var(--space-3);
  }

  .card {
    margin: var(--space-3);
    border-radius: var(--radius-lg);
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .modal {
    margin: var(--space-3);
    max-width: calc(100% - var(--space-6));
  }

  .table {
    font-size: var(--font-size-sm);
  }

  .table th,
  .table td {
    padding: var(--space-2);
  }

  .sidebar {
    padding: var(--space-4);
  }

  .sidebar-menu-link {
    padding: var(--space-2) var(--space-3);
  }

  /* Hide text on mobile sidebar */
  .mobile-sidebar .sidebar-menu-link span {
    display: none;
  }

  .mobile-sidebar .sidebar-menu-link {
    justify-content: center;
  }

  /* Stack columns on mobile */
  .row {
    flex-direction: column;
  }

  .col,
  .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
  .col-7, .col-8, .col-9, .col-10, .col-11, .col-12 {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: var(--space-4);
  }
}

/* Tablet */
@media (min-width: 641px) and (max-width: 1024px) {
  .container {
    padding: 0 var(--space-4);
  }

  .card {
    margin: var(--space-4);
  }

  .modal {
    max-width: 600px;
  }
}

/* Desktop */
@media (min-width: 1025px) {
  .container {
    padding: 0 var(--space-6);
  }

  .card:hover {
    transform: translateY(-4px);
  }

  .btn:hover {
    transform: translateY(-2px);
  }
}

/* ===== ACCESSIBILITY ===== */

/* Focus styles */
*:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.btn:focus,
.form-control:focus,
.form-select:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --gray-100: #000000;
    --gray-200: #000000;
    --gray-300: #000000;
  }

  .card {
    border: 2px solid var(--gray-900);
  }

  .btn {
    border: 2px solid currentColor;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .sidebar,
  .btn,
  .modal,
  .dropdown {
    display: none !important;
  }

  .card {
    box-shadow: none;
    border: 1px solid var(--gray-300);
  }

  body {
    background: white;
    color: black;
  }
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--gray-300) var(--gray-100);
}

/* ===== EDUCATIONAL PLATFORM SPECIFIC STYLES ===== */

/* Quiz Card Styles - User Preferred Design */
.quiz-card {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  transition: var(--transition-normal);
  overflow: hidden;
  position: relative;
  margin-bottom: var(--space-3);
}

.quiz-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
  border-color: var(--primary);
}

/* Quiz Answer States - User Preferred Green/Red Backgrounds */
.quiz-card.correct,
.quiz-option.correct {
  border-color: var(--success);
  background: var(--success) !important;
  color: var(--white) !important;
}

.quiz-card.incorrect,
.quiz-option.incorrect {
  border-color: var(--danger);
  background: var(--danger) !important;
  color: var(--white) !important;
}

.quiz-card.selected,
.quiz-option.selected {
  border-color: var(--primary);
  background: var(--primary) !important;
  color: var(--white) !important;
}

/* Quiz Interface - User Preferred Styling */
.quiz-question-container {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-3);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.quiz-question-number {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--primary);
  margin-bottom: var(--space-2);
}

.quiz-question-text {
  font-size: var(--font-size-base);
  color: var(--gray-800);
  margin-bottom: var(--space-4);
  line-height: 1.6;
}

.quiz-options-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.quiz-option {
  padding: var(--space-3);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  background: var(--white);
  cursor: pointer;
  transition: var(--transition-normal);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.quiz-option:hover {
  border-color: var(--primary-300);
  background: var(--primary-50);
}

.quiz-option-letter {
  font-weight: 600;
  color: var(--gray-600);
  min-width: 24px;
  text-align: center;
}

.quiz-option.selected .quiz-option-letter {
  color: var(--white);
}

.quiz-option.correct .quiz-option-letter,
.quiz-option.incorrect .quiz-option-letter {
  color: var(--white);
}

/* Quiz Navigation - User Preferred Bottom-Centered */
.quiz-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background: var(--white);
  border-top: 1px solid var(--gray-200);
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* Responsive quiz navigation */
@media (max-width: 480px) {
  .quiz-navigation {
    padding: var(--space-2);
    gap: var(--space-1);
    flex-direction: column;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .quiz-navigation {
    padding: var(--space-3);
    gap: var(--space-2);
  }
}

.quiz-nav-btn {
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  font-size: var(--font-size-sm);
}

/* Responsive quiz navigation buttons */
@media (max-width: 480px) {
  .quiz-nav-btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-xs);
    width: 100%;
    margin-bottom: var(--space-1);
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .quiz-nav-btn {
    padding: var(--space-2) var(--space-5);
    font-size: var(--font-size-xs);
  }
}

.quiz-nav-btn.primary {
  background: var(--primary);
  color: var(--white);
}

.quiz-nav-btn.primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.quiz-nav-btn.secondary {
  background: var(--gray-200);
  color: var(--gray-700);
}

.quiz-nav-btn.secondary:hover {
  background: var(--gray-300);
}

/* Quiz Timer - User Preferred Blue Theme with Red Warning */
.quiz-timer {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary);
  color: var(--white);
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--font-size-lg);
  min-width: 120px;
  transition: var(--transition-normal);
}

/* Responsive quiz timer */
@media (max-width: 480px) {
  .quiz-timer {
    padding: var(--space-2);
    font-size: var(--font-size-sm);
    min-width: 100px;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .quiz-timer {
    padding: var(--space-2);
    font-size: var(--font-size-base);
    min-width: 110px;
  }
}

.quiz-timer.warning {
  background: var(--danger);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Quiz Progress Line - User Preferred Blue */
.quiz-progress-container {
  padding: var(--space-3) var(--space-4);
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
}

.quiz-progress-line {
  width: 100%;
  height: 4px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.quiz-progress-fill {
  height: 100%;
  background: var(--primary);
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
}

.quiz-question-counter {
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin-top: var(--space-2);
}

/* Study Material Cards - Modern Blue Theme */
.study-card {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  transition: var(--transition-normal);
  overflow: hidden;
  margin-bottom: var(--space-4);
}

.study-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--primary);
}

.study-card-header {
  background: var(--primary);
  color: var(--white);
  padding: var(--space-4);
  border-bottom: 1px solid var(--primary-dark);
}

.study-card-body {
  padding: var(--space-4);
}

.study-card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--white);
  margin-bottom: var(--space-2);
}

.study-card-meta {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.9);
}

.study-card-description {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  margin-bottom: var(--space-3);
}

.study-card-actions {
  display: flex;
  gap: var(--space-2);
  justify-content: flex-end;
}

/* Study Material Tabs */
.study-tabs {
  display: flex;
  gap: var(--space-1);
  background: var(--gray-100);
  padding: var(--space-1);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
}

/* Responsive study tabs */
@media (max-width: 480px) {
  .study-tabs {
    flex-direction: column;
    gap: var(--space-1);
    padding: var(--space-1);
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .study-tabs {
    flex-wrap: wrap;
    gap: var(--space-1);
  }
}

.study-tab {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  border: none;
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--gray-600);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

/* Responsive study tab */
@media (max-width: 480px) {
  .study-tab {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-xs);
    gap: var(--space-1);
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .study-tab {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-sm);
  }
}

.study-tab.active {
  background: var(--primary);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.study-tab:hover:not(.active) {
  background: var(--gray-200);
  color: var(--gray-700);
}

/* Hub/Dashboard Styles - Amazing Icons & Cool Animations */
.hub-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--white) 50%, var(--primary-50) 100%);
  position: relative;
  overflow: hidden;
}

.hub-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e5e7eb" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}

.hub-welcome {
  text-align: center;
  padding: var(--space-8) var(--space-4);
  position: relative;
  z-index: 2;
}

.hub-welcome h1 {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 50%, var(--primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-4);
  animation: fadeInUp 0.8s ease-out;
}

.hub-welcome p {
  font-size: var(--font-size-xl);
  color: var(--gray-600);
  margin-bottom: var(--space-6);
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hub-quote {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin: var(--space-6) auto;
  max-width: 600px;
  box-shadow: var(--shadow-lg);
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.hub-quote-text {
  font-size: var(--font-size-lg);
  font-style: italic;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

.hub-quote-author {
  font-size: var(--font-size-sm);
  color: var(--primary);
  font-weight: 600;
}

/* Hub Navigation Cards - Responsive */
.hub-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-4);
  padding: var(--space-4);
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

/* Responsive hub grid */
@media (max-width: 480px) {
  .hub-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
    padding: var(--space-2);
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .hub-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-3);
    padding: var(--space-3);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .hub-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-4);
  }
}

.hub-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  transition: var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out;
}

.hub-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.hub-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary);
}

.hub-card:hover::before {
  transform: scaleX(1);
}

.hub-card-icon {
  width: 60px;
  height: 60px;
  background: var(--primary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-4);
  color: var(--white);
  font-size: 1.5rem;
  transition: var(--transition-normal);
}

/* Responsive hub card icons */
@media (max-width: 480px) {
  .hub-card-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
    margin-bottom: var(--space-3);
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .hub-card-icon {
    width: 55px;
    height: 55px;
    font-size: 1.375rem;
  }
}

.hub-card:hover .hub-card-icon {
  transform: scale(1.1) rotate(5deg);
  background: var(--primary-dark);
}

.hub-card-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-2);
}

.hub-card-description {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

/* Responsive hub card text */
@media (max-width: 480px) {
  .hub-card-title {
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-1);
  }

  .hub-card-description {
    font-size: var(--font-size-xs);
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .hub-card-title {
    font-size: var(--font-size-lg);
  }

  .hub-card-description {
    font-size: var(--font-size-xs);
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-10px); }
  70% { transform: translateY(-5px); }
  90% { transform: translateY(-2px); }
}

/* Animation Classes */
.animate-fadeInUp { animation: fadeInUp 0.6s ease-out; }
.animate-fadeIn { animation: fadeIn 0.6s ease-out; }
.animate-slideInRight { animation: slideInRight 0.5s ease-out; }
.animate-float { animation: float 3s ease-in-out infinite; }
.animate-bounce { animation: bounce 2s infinite; }

/* Delay Classes */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }

/* Enhanced Button Styles - Blue Theme */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: var(--font-size-sm);
  line-height: 1;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-primary {
  background: var(--primary);
  color: var(--white);
  box-shadow: var(--shadow-primary);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 8px 25px -5px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
  background: var(--white);
  color: var(--primary);
  border: 2px solid var(--primary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--primary);
  color: var(--white);
  transform: translateY(-1px);
}

.btn-success {
  background: var(--success);
  color: var(--white);
  box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.15);
}

.btn-success:hover:not(:disabled) {
  background: var(--success-dark);
  transform: translateY(-1px);
}

.btn-danger {
  background: var(--danger);
  color: var(--white);
  box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.15);
}

.btn-danger:hover:not(:disabled) {
  background: var(--danger-dark);
  transform: translateY(-1px);
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-base);
}

/* ===== UTILITY CLASSES ===== */

/* Display utilities */
.d-flex { display: flex !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-none { display: none !important; }

/* Flexbox utilities */
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }

.align-items-center { align-items: center !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-stretch { align-items: stretch !important; }

.flex-direction-column { flex-direction: column !important; }
.flex-direction-row { flex-direction: row !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

/* Spacing utilities */
.gap-1 { gap: var(--space-1) !important; }
.gap-2 { gap: var(--space-2) !important; }
.gap-3 { gap: var(--space-3) !important; }
.gap-4 { gap: var(--space-4) !important; }
.gap-5 { gap: var(--space-5) !important; }
.gap-6 { gap: var(--space-6) !important; }

/* Margin utilities */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--space-1) !important; }
.m-2 { margin: var(--space-2) !important; }
.m-3 { margin: var(--space-3) !important; }
.m-4 { margin: var(--space-4) !important; }
.m-5 { margin: var(--space-5) !important; }
.m-6 { margin: var(--space-6) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--space-1) !important; }
.mt-2 { margin-top: var(--space-2) !important; }
.mt-3 { margin-top: var(--space-3) !important; }
.mt-4 { margin-top: var(--space-4) !important; }
.mt-5 { margin-top: var(--space-5) !important; }
.mt-6 { margin-top: var(--space-6) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--space-1) !important; }
.mb-2 { margin-bottom: var(--space-2) !important; }
.mb-3 { margin-bottom: var(--space-3) !important; }
.mb-4 { margin-bottom: var(--space-4) !important; }
.mb-5 { margin-bottom: var(--space-5) !important; }
.mb-6 { margin-bottom: var(--space-6) !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: var(--space-1) !important; }
.ml-2 { margin-left: var(--space-2) !important; }
.ml-3 { margin-left: var(--space-3) !important; }
.ml-4 { margin-left: var(--space-4) !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: var(--space-1) !important; }
.mr-2 { margin-right: var(--space-2) !important; }
.mr-3 { margin-right: var(--space-3) !important; }
.mr-4 { margin-right: var(--space-4) !important; }

/* Padding utilities */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--space-1) !important; }
.p-2 { padding: var(--space-2) !important; }
.p-3 { padding: var(--space-3) !important; }
.p-4 { padding: var(--space-4) !important; }
.p-5 { padding: var(--space-5) !important; }
.p-6 { padding: var(--space-6) !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: var(--space-1) !important; }
.pt-2 { padding-top: var(--space-2) !important; }
.pt-3 { padding-top: var(--space-3) !important; }
.pt-4 { padding-top: var(--space-4) !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: var(--space-1) !important; }
.pb-2 { padding-bottom: var(--space-2) !important; }
.pb-3 { padding-bottom: var(--space-3) !important; }
.pb-4 { padding-bottom: var(--space-4) !important; }

.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: var(--space-1) !important; }
.pl-2 { padding-left: var(--space-2) !important; }
.pl-3 { padding-left: var(--space-3) !important; }
.pl-4 { padding-left: var(--space-4) !important; }

.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: var(--space-1) !important; }
.pr-2 { padding-right: var(--space-2) !important; }
.pr-3 { padding-right: var(--space-3) !important; }
.pr-4 { padding-right: var(--space-4) !important; }

/* Text utilities */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.text-primary { color: var(--primary) !important; }
.text-secondary { color: var(--gray-600) !important; }
.text-success { color: var(--success) !important; }
.text-danger { color: var(--danger) !important; }
.text-warning { color: var(--warning) !important; }
.text-info { color: var(--info) !important; }
.text-white { color: var(--white) !important; }
.text-dark { color: var(--gray-900) !important; }
.text-muted { color: var(--gray-500) !important; }

.font-weight-normal { font-weight: 400 !important; }
.font-weight-medium { font-weight: 500 !important; }
.font-weight-semibold { font-weight: 600 !important; }
.font-weight-bold { font-weight: 700 !important; }

.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }

/* Background utilities */
.bg-primary { background-color: var(--primary) !important; }
.bg-secondary { background-color: var(--gray-600) !important; }
.bg-success { background-color: var(--success) !important; }
.bg-danger { background-color: var(--danger) !important; }
.bg-warning { background-color: var(--warning) !important; }
.bg-info { background-color: var(--info) !important; }
.bg-white { background-color: var(--white) !important; }
.bg-light { background-color: var(--gray-100) !important; }
.bg-dark { background-color: var(--gray-900) !important; }

/* Border utilities */
.border { border: 1px solid var(--gray-200) !important; }
.border-0 { border: 0 !important; }
.border-primary { border-color: var(--primary) !important; }
.border-secondary { border-color: var(--gray-300) !important; }
.border-success { border-color: var(--success) !important; }
.border-danger { border-color: var(--danger) !important; }

.rounded { border-radius: var(--radius-md) !important; }
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

/* Width utilities */
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

/* Grid utilities */
.grid { display: grid !important; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
.grid-cols-auto { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important; }

/* Responsive grid utilities */
@media (max-width: 480px) {
  .grid-cols-2, .grid-cols-3, .grid-cols-4, .grid-cols-auto {
    grid-template-columns: 1fr !important;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .grid-cols-3, .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
  .grid-cols-auto {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid-cols-4 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }
}

/* Height utilities */
.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

/* Position utilities */
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* Shadow utilities */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-none { box-shadow: none !important; }

/* ===== GLOBAL OVERRIDES FOR CONSISTENCY ===== */

/* ===== RESPONSIVE CONTAINER SYSTEM ===== */
.container, .container-fluid {
  background: var(--white) !important;
  width: 100% !important;
  margin-left: auto !important;
  margin-right: auto !important;
  padding-left: var(--space-4) !important;
  padding-right: var(--space-4) !important;
}

.container {
  max-width: 1200px !important;
}

/* Responsive container padding */
@media (max-width: 480px) {
  .container, .container-fluid {
    padding-left: var(--space-2) !important;
    padding-right: var(--space-2) !important;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .container, .container-fluid {
    padding-left: var(--space-3) !important;
    padding-right: var(--space-3) !important;
  }
}

@media (min-width: 769px) {
  .container {
    max-width: 1200px !important;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1400px !important;
  }
}

/* Override any conflicting button styles */
button {
  font-family: inherit !important;
  border: none !important;
  cursor: pointer !important;
}

/* Ensure consistent spacing */
.row {
  margin: 0 !important;
}

.col, [class*="col-"] {
  padding: var(--space-2) !important;
}

/* Override any conflicting text colors */
h1, h2, h3, h4, h5, h6 {
  color: var(--gray-900) !important;
}

p {
  color: var(--gray-700) !important;
}

/* Ensure consistent link styling */
a {
  color: var(--primary) !important;
  text-decoration: none !important;
}

a:hover {
  color: var(--primary-dark) !important;
}

/* Override any conflicting list styles */
ul, ol {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Ensure consistent table styling */
table {
  width: 100% !important;
  border-collapse: collapse !important;
}

th, td {
  padding: var(--space-3) !important;
  border-bottom: 1px solid var(--gray-200) !important;
}

th {
  background: var(--gray-50) !important;
  font-weight: 600 !important;
  color: var(--gray-900) !important;
}

/* ===== RESPONSIVE DESIGN SYSTEM ===== */

/* Base responsive typography */
html {
  font-size: 16px;
}

/* Responsive font sizes */
@media (max-width: 480px) {
  html { font-size: 14px; }

  h1 { font-size: 1.75rem !important; }
  h2 { font-size: 1.5rem !important; }
  h3 { font-size: 1.25rem !important; }
  h4 { font-size: 1.125rem !important; }
  h5 { font-size: 1rem !important; }
  h6 { font-size: 0.875rem !important; }
}

@media (min-width: 481px) and (max-width: 768px) {
  html { font-size: 15px; }

  h1 { font-size: 2rem !important; }
  h2 { font-size: 1.75rem !important; }
  h3 { font-size: 1.5rem !important; }
  h4 { font-size: 1.25rem !important; }
  h5 { font-size: 1.125rem !important; }
  h6 { font-size: 1rem !important; }
}

@media (min-width: 769px) and (max-width: 1024px) {
  html { font-size: 16px; }
}

@media (min-width: 1025px) {
  html { font-size: 16px; }
}

/* Responsive icon sizes */
.icon-xs { font-size: 0.75rem !important; }
.icon-sm { font-size: 1rem !important; }
.icon-md { font-size: 1.25rem !important; }
.icon-lg { font-size: 1.5rem !important; }
.icon-xl { font-size: 2rem !important; }
.icon-2xl { font-size: 2.5rem !important; }
.icon-3xl { font-size: 3rem !important; }

/* Responsive icon scaling */
@media (max-width: 480px) {
  .icon-xs { font-size: 0.625rem !important; }
  .icon-sm { font-size: 0.875rem !important; }
  .icon-md { font-size: 1rem !important; }
  .icon-lg { font-size: 1.25rem !important; }
  .icon-xl { font-size: 1.5rem !important; }
  .icon-2xl { font-size: 2rem !important; }
  .icon-3xl { font-size: 2.5rem !important; }
}

@media (min-width: 481px) and (max-width: 768px) {
  .icon-xs { font-size: 0.75rem !important; }
  .icon-sm { font-size: 0.875rem !important; }
  .icon-md { font-size: 1.125rem !important; }
  .icon-lg { font-size: 1.375rem !important; }
  .icon-xl { font-size: 1.75rem !important; }
  .icon-2xl { font-size: 2.25rem !important; }
  .icon-3xl { font-size: 2.75rem !important; }
}

/* Mobile-first responsive utilities */
@media (max-width: 480px) {
  .container {
    padding: var(--space-2) !important;
    margin: 0 !important;
  }

  .card {
    margin-bottom: var(--space-3) !important;
    padding: var(--space-3) !important;
  }

  .btn {
    width: 100% !important;
    margin-bottom: var(--space-2) !important;
    padding: var(--space-3) var(--space-4) !important;
    font-size: 0.875rem !important;
  }

  .sidebar {
    width: 100% !important;
    height: auto !important;
    position: relative !important;
  }

  .layout {
    flex-direction: column !important;
  }

  .content {
    padding: var(--space-2) !important;
  }

  .header {
    padding: var(--space-3) !important;
    margin-bottom: var(--space-2) !important;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .container {
    padding: var(--space-3) !important;
  }

  .card {
    margin-bottom: var(--space-3) !important;
    padding: var(--space-4) !important;
  }

  .btn {
    padding: var(--space-3) var(--space-5) !important;
    font-size: 0.875rem !important;
  }

  .content {
    padding: var(--space-3) !important;
  }

  .header {
    padding: var(--space-4) !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .container {
    padding: var(--space-4) !important;
  }

  .card {
    padding: var(--space-4) !important;
  }

  .content {
    padding: var(--space-4) !important;
  }
}

/* Ensure quiz components use modern styling */
.quiz-container * {
  font-family: inherit !important;
}

/* Ensure study material components use modern styling */
.study-material-modern * {
  font-family: inherit !important;
}

/* Ensure hub components use modern styling */
.hub-container * {
  font-family: inherit !important;
}

/* Force modern styling on all major components */
.layout, .sidebar, .content, .header {
  font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif !important;
}

/* ===== CONSISTENT ICON SYSTEM ===== */
.icon, [class*="icon-"], .fa, [class*="fa-"], svg {
  display: inline-block !important;
  vertical-align: middle !important;
  line-height: 1 !important;
}

/* Standard icon sizes */
.fa, [class*="fa-"], svg {
  font-size: 1.25rem !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
}

/* Specific icon size classes */
.text-xs .fa, .text-xs [class*="fa-"], .text-xs svg { font-size: 0.75rem !important; width: 0.75rem !important; height: 0.75rem !important; }
.text-sm .fa, .text-sm [class*="fa-"], .text-sm svg { font-size: 1rem !important; width: 1rem !important; height: 1rem !important; }
.text-base .fa, .text-base [class*="fa-"], .text-base svg { font-size: 1.25rem !important; width: 1.25rem !important; height: 1.25rem !important; }
.text-lg .fa, .text-lg [class*="fa-"], .text-lg svg { font-size: 1.5rem !important; width: 1.5rem !important; height: 1.5rem !important; }
.text-xl .fa, .text-xl [class*="fa-"], .text-xl svg { font-size: 2rem !important; width: 2rem !important; height: 2rem !important; }
.text-2xl .fa, .text-2xl [class*="fa-"], .text-2xl svg { font-size: 2.5rem !important; width: 2.5rem !important; height: 2.5rem !important; }
.text-3xl .fa, .text-3xl [class*="fa-"], .text-3xl svg { font-size: 3rem !important; width: 3rem !important; height: 3rem !important; }
.text-4xl .fa, .text-4xl [class*="fa-"], .text-4xl svg { font-size: 3.5rem !important; width: 3.5rem !important; height: 3.5rem !important; }

/* Menu item icons */
.menu-item .fa, .menu-item [class*="fa-"], .menu-item svg {
  font-size: 1.25rem !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
  margin-right: var(--space-3) !important;
}

/* Hub card icons */
.hub-card-icon .fa, .hub-card-icon [class*="fa-"], .hub-card-icon svg {
  font-size: 2rem !important;
  width: 2rem !important;
  height: 2rem !important;
}

/* Header icons */
.header .fa, .header [class*="fa-"], .header svg {
  font-size: 1.5rem !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
}

/* Button icons */
.btn .fa, .btn [class*="fa-"], .btn svg {
  font-size: 1rem !important;
  width: 1rem !important;
  height: 1rem !important;
  margin-right: var(--space-2) !important;
}

/* Card header icons */
.card-header .fa, .card-header [class*="fa-"], .card-header svg {
  font-size: 1.25rem !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
}

/* Override any conflicting modal styles */
.modal, .modal-content {
  border-radius: var(--radius-lg) !important;
  border: none !important;
  box-shadow: var(--shadow-xl) !important;
}

.modal-header {
  background: var(--primary) !important;
  color: white !important;
  border-bottom: none !important;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0 !important;
}

.modal-footer {
  border-top: 1px solid var(--gray-200) !important;
  background: var(--gray-50) !important;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg) !important;
}

/* Ensure consistent loading states */
.loading, .spinner {
  color: var(--primary) !important;
}

/* Override any conflicting alert styles */
.alert {
  border-radius: var(--radius-lg) !important;
  border: none !important;
  padding: var(--space-4) !important;
}

.alert-success {
  background: var(--success-light) !important;
  color: var(--success-dark) !important;
}

.alert-danger {
  background: var(--danger-light) !important;
  color: var(--danger-dark) !important;
}

.alert-warning {
  background: var(--warning-light) !important;
  color: var(--warning-dark) !important;
}

.alert-info {
  background: var(--info-light) !important;
  color: var(--info-dark) !important;
}

/* ===== FINAL RESPONSIVE TEXT SCALING ===== */

/* Ensure consistent text scaling across all components */
@media (max-width: 480px) {
  /* Small text elements */
  .text-xs, small, .small { font-size: 0.625rem !important; }
  .text-sm { font-size: 0.75rem !important; }
  .text-base, p, span, div { font-size: 0.875rem !important; }
  .text-lg { font-size: 1rem !important; }
  .text-xl { font-size: 1.125rem !important; }
  .text-2xl { font-size: 1.25rem !important; }

  /* Button text */
  .btn { font-size: 0.75rem !important; }
  .btn-sm { font-size: 0.625rem !important; }
  .btn-lg { font-size: 0.875rem !important; }

  /* Menu items */
  .menu-item { font-size: 0.75rem !important; }

  /* Card text */
  .card-title { font-size: 1rem !important; }
  .card-text { font-size: 0.75rem !important; }
}

@media (min-width: 481px) and (max-width: 768px) {
  /* Medium screen text scaling */
  .text-xs, small, .small { font-size: 0.75rem !important; }
  .text-sm { font-size: 0.875rem !important; }
  .text-base, p, span, div { font-size: 0.875rem !important; }
  .text-lg { font-size: 1rem !important; }
  .text-xl { font-size: 1.125rem !important; }
  .text-2xl { font-size: 1.375rem !important; }

  /* Button text */
  .btn { font-size: 0.875rem !important; }
  .btn-sm { font-size: 0.75rem !important; }
  .btn-lg { font-size: 1rem !important; }

  /* Menu items */
  .menu-item { font-size: 0.875rem !important; }
}

/* Ensure all icons scale properly with text */
@media (max-width: 768px) {
  .fa, [class*="fa-"], svg {
    vertical-align: middle !important;
  }

  /* Scale icons with their parent text size */
  .text-xs .fa, .text-xs [class*="fa-"], .text-xs svg { font-size: 0.75rem !important; }
  .text-sm .fa, .text-sm [class*="fa-"], .text-sm svg { font-size: 0.875rem !important; }
  .text-base .fa, .text-base [class*="fa-"], .text-base svg { font-size: 1rem !important; }
  .text-lg .fa, .text-lg [class*="fa-"], .text-lg svg { font-size: 1.125rem !important; }
  .text-xl .fa, .text-xl [class*="fa-"], .text-xl svg { font-size: 1.25rem !important; }
}

/* Performance Indicators */
.performance-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.performance-excellent {
  background: var(--success-light);
  color: #065f46;
}

.performance-good {
  background: var(--primary-100);
  color: var(--primary-800);
}

.performance-average {
  background: var(--warning-light);
  color: #92400e;
}

.performance-poor {
  background: var(--danger-light);
  color: #991b1b;
}

/* Login/Register Page Styles */
.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
}

.auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  padding: var(--space-8);
  width: 100%;
  max-width: 400px;
}

.auth-logo {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--space-6);
  display: block;
  border-radius: var(--radius-xl);
}

.auth-title {
  text-align: center;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-6);
}

.auth-subtitle {
  text-align: center;
  color: var(--gray-600);
  margin-bottom: var(--space-8);
}

/* Dashboard Stats Cards */
.stats-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-100);
  padding: var(--space-6);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
}

.stats-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.stats-value {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--primary);
  margin-bottom: var(--space-2);
}

.stats-label {
  color: var(--gray-600);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  font-size: var(--font-size-sm);
}

/* Success/Error States */
.success-state {
  color: var(--success);
  background: var(--success-light);
  border-color: var(--success);
}

.error-state {
  color: var(--danger);
  background: var(--danger-light);
  border-color: var(--danger);
}

.warning-state {
  color: var(--warning);
  background: var(--warning-light);
  border-color: var(--warning);
}

.info-state {
  color: var(--info);
  background: var(--info-light);
  border-color: var(--info);
}

/* CSS Variables for Theme */
:root {
  /* Primary Blue Theme */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --gradient-blue-sky: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
  --gradient-blue-ocean: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  
  /* Shadows */
  --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-large: 0 10px 40px -10px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-blue: 0 4px 14px 0 rgba(59, 130, 246, 0.15);
  --shadow-blue-lg: 0 10px 40px -10px rgba(59, 130, 246, 0.25);
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* Dark Mode Variables */
.dark {
  --color-primary-50: #172554;
  --color-primary-100: #1e3a8a;
  --color-primary-200: #1e40af;
  --color-primary-300: #1d4ed8;
  --color-primary-400: #2563eb;
  --color-primary-500: #3b82f6;
  --color-primary-600: #60a5fa;
  --color-primary-700: #93c5fd;
  --color-primary-800: #bfdbfe;
  --color-primary-900: #dbeafe;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: #1f2937;
  background-color: #ffffff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern Component Classes */
@layer components {
  /* Modern Button Styles */
  .btn-modern {
    @apply inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn-modern bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
  }
  
  .btn-secondary {
    @apply btn-modern bg-white text-primary-600 border border-primary-200 hover:bg-primary-50 focus:ring-primary-500 shadow-sm hover:shadow-md;
  }
  
  .btn-ghost {
    @apply btn-modern bg-transparent text-primary-600 hover:bg-primary-50 focus:ring-primary-500;
  }
  
  /* Modern Card Styles */
  .card-modern {
    @apply bg-white rounded-xl shadow-soft border border-gray-100 transition-all duration-300 hover:shadow-medium;
  }
  
  .card-interactive {
    @apply card-modern cursor-pointer hover:shadow-large hover:-translate-y-1 transform;
  }
  
  .card-glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20 rounded-xl shadow-soft;
  }
  
  /* Modern Input Styles */
  .input-modern {
    @apply w-full px-4 py-3 text-gray-900 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 placeholder-gray-400;
  }
  
  /* Modern Navigation */
  .nav-modern {
    @apply bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50;
  }

  /* Enhanced Header Styles */
  .hub-button {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .hub-button:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.5);
  }

  .hub-button:active {
    transform: translateY(-1px) scale(1.02);
  }

  .brainwave-heading {
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
    letter-spacing: -0.05em;
    line-height: 0.9;
    font-weight: 900;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .user-avatar {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: transparent !important;
    position: relative;
  }

  .user-avatar:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow: 0 15px 35px -5px rgba(59, 130, 246, 0.4), 0 5px 15px -5px rgba(0, 0, 0, 0.1);
  }

  /* Enhanced Profile Styles */
  .user-info-desktop,
  .user-info-tablet {
    transition: all 0.2s ease;
  }

  .user-info-desktop:hover,
  .user-info-tablet:hover {
    transform: translateY(-1px);
  }

  /* Ensure user profile container has no background */
  .user-profile-container {
    background: transparent !important;
    position: relative;
    z-index: auto;
  }

  /* Ensure user info sections are properly styled */
  .user-info-desktop,
  .user-info-tablet,
  .user-info-mobile {
    background: transparent !important;
    position: relative;
    z-index: auto;
  }

  /* Remove any potential debugging overlays or blue boxes */
  .nav-modern *::before,
  .nav-modern *::after {
    background: transparent !important;
    border: none !important;
  }

  /* Ensure header elements don't have unwanted backgrounds */
  .nav-modern .flex {
    background: transparent !important;
  }

  /* Remove any potential browser debugging styles */
  .nav-modern [style*="background"] {
    background: transparent !important;
  }

  /* Extra small screens (xs) - Custom breakpoint */
  @media (min-width: 375px) {
    .xs\:px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
    .xs\:px-4 { padding-left: 1rem; padding-right: 1rem; }
    .xs\:py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
    .xs\:h-14 { height: 3.5rem; }
    .xs\:h-9 { height: 2.25rem; }
    .xs\:w-9 { width: 2.25rem; }
    .xs\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
    .xs\:text-base { font-size: 1rem; line-height: 1.5rem; }
    .xs\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
    .xs\:space-x-2 > :not([hidden]) ~ :not([hidden]) { margin-left: 0.5rem; }
    .xs\:max-w-20 { max-width: 5rem; }
    .xs\:inline { display: inline; }
  }

  /* Mobile-first responsive header adjustments */
  @media (max-width: 374px) {
    .nav-modern .hub-button {
      padding: 0.375rem 0.5rem;
      font-size: 0.75rem;
    }

    .nav-modern .brainwave-heading {
      font-size: 1rem;
      max-width: 8rem;
    }

    .nav-modern .user-avatar {
      width: 1.75rem;
      height: 1.75rem;
    }

    .nav-modern .user-info-mobile {
      font-size: 0.625rem;
    }
  }

  /* Tablet landscape optimizations */
  @media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
    .nav-modern {
      height: auto;
      min-height: 3.5rem;
    }

    .nav-modern .brainwave-heading {
      font-size: 1.5rem;
    }
  }

  /* Large screen optimizations */
  @media (min-width: 1440px) {
    .nav-modern .brainwave-heading {
      font-size: 2.5rem;
    }

    .nav-modern .user-avatar {
      width: 3.5rem;
      height: 3.5rem;
    }
  }

  /* Custom utility classes for enhanced styling */
  .border-3 {
    border-width: 3px;
  }

  .w-13 {
    width: 3.25rem;
  }

  .h-13 {
    height: 3.25rem;
  }

  .w-14 {
    width: 3.5rem;
  }

  .h-14 {
    height: 3.5rem;
  }

  /* Glassmorphism effects */
  .glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  /* Enhanced gradient text */
  .gradient-text-enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .nav-item {
    @apply px-4 py-2 text-gray-600 hover:text-primary-600 rounded-lg hover:bg-primary-50 transition-all duration-200 font-medium;
  }
  
  .nav-item-active {
    @apply nav-item text-primary-600 bg-primary-50;
  }
  
  /* Modern Sidebar */
  .sidebar-modern {
    @apply bg-gradient-to-b from-primary-600 to-primary-800 text-white shadow-large;
  }
  
  .sidebar-item {
    @apply flex items-center px-4 py-3 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200 cursor-pointer;
  }
  
  .sidebar-item-active {
    @apply sidebar-item text-white bg-white/20 shadow-sm;
  }
  
  /* Modern Layout */
  .layout-modern {
    @apply min-h-screen bg-gradient-to-br from-gray-50 to-blue-50;
  }
  
  .container-modern {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  /* Modern Typography */
  .heading-1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight;
  }
  
  .heading-2 {
    @apply text-3xl md:text-4xl font-bold text-gray-900 leading-tight;
  }
  
  .heading-3 {
    @apply text-2xl md:text-3xl font-semibold text-gray-900 leading-tight;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent;
  }
  
  /* Modern Animations */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }
  
  .animate-slide-in-right {
    animation: slideInRight 0.5s ease-out forwards;
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  /* Modern Loading States */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-200 border-t-primary-600;
  }
  
  /* Modern Quiz Styles */
  .quiz-card {
    @apply card-modern p-6 hover:shadow-blue transform hover:scale-105;
  }
  
  .quiz-option {
    @apply p-4 border-2 border-gray-200 rounded-lg cursor-pointer transition-all duration-200 hover:border-primary-300 hover:bg-primary-50;
  }
  
  .quiz-option-selected {
    @apply quiz-option border-primary-500 bg-primary-50 text-primary-700;
  }
  
  .quiz-option-correct {
    @apply quiz-option border-success-500 bg-success-50 text-success-700;
  }
  
  .quiz-option-incorrect {
    @apply quiz-option border-error-500 bg-error-50 text-error-700;
  }
  
  /* Modern Progress Bar */
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
  }
  
  .progress-fill {
    @apply h-full bg-gradient-to-r from-primary-500 to-blue-500 rounded-full transition-all duration-500 ease-out;
  }
  
  /* Modern Badge */
  .badge-modern {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge-modern bg-primary-100 text-primary-800;
  }
  
  .badge-success {
    @apply badge-modern bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge-modern bg-warning-100 text-warning-800;
  }
  
  .badge-error {
    @apply badge-modern bg-error-100 text-error-800;
  }
}

/* Responsive Design Utilities */
@layer utilities {
  .text-responsive {
    @apply text-sm sm:text-base md:text-lg;
  }
  
  .padding-responsive {
    @apply p-4 sm:p-6 md:p-8;
  }
  
  .margin-responsive {
    @apply m-4 sm:m-6 md:m-8;
  }
  
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* ===== MODERN UTILITY CLASSES ===== */

/* Glassmorphism */
.glass {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
}

/* Modern Cards */
.card-modern {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-blur);
  transition: var(--transition-normal);
}

.card-modern:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-2xl);
}

/* Gradient Backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

.bg-gradient-blue {
  background: linear-gradient(135deg, #007BFF 0%, #0056D2 50%, #4338CA 100%);
}

.bg-gradient-page {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #f8fafc 50%, #ffffff 100%);
}

/* Text Gradients */
.text-gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Animations */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-bounce-gentle {
  animation: bounceGentle 2s ease-in-out infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceGentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

/* ===== END OF BRAINWAVE MODERN DESIGN SYSTEM ===== */
