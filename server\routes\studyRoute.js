const router = require("express").Router();
const Notes = require("../models/studyNotes");
const PastPapers = require("../models/studyPastPapers");
const Videos = require("../models/studyVideos");
const Books = require('../models/studyBooks');
const authMiddleware = require("../middlewares/authMiddleware");
const User = require("../models/userModel");
const multer = require("multer");
const AWS = require("aws-sdk");
const { v4: uuidv4 } = require("uuid");
const ffmpeg = require("fluent-ffmpeg");
const ffmpegStatic = require("ffmpeg-static");
const path = require("path");
const fs = require("fs");
const subtitleService = require("../services/subtitleService");

// Set ffmpeg path
try {
    ffmpeg.setFfmpegPath(ffmpegStatic);
    console.log("✅ FFmpeg configured with static binary");
} catch (error) {
    console.warn("⚠️ FFmpeg static binary not found, trying system FFmpeg");
    try {
        const ffmpegInstaller = require('@ffmpeg-installer/ffmpeg');
        ffmpeg.setFfmpegPath(ffmpegInstaller.path);
        console.log("✅ FFmpeg configured with installer package");
    } catch (installerError) {
        console.warn("⚠️ FFmpeg installer package not found, thumbnails will be disabled");
    }
}

// Enhanced function to generate video thumbnail with multiple fallback methods
const generateVideoThumbnail = async (videoBuffer, videoFilename = null) => {
    return new Promise(async (resolve, reject) => {
        const tempVideoPath = path.join(__dirname, `../temp/video-${uuidv4()}.mp4`);
        const tempThumbnailPath = path.join(__dirname, `../temp/thumbnail-${uuidv4()}.jpg`);

        // Ensure temp directory exists
        const tempDir = path.dirname(tempVideoPath);
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }

        try {
            // Write video buffer to temporary file
            fs.writeFileSync(tempVideoPath, videoBuffer);
            console.log("📁 Temporary video file created for thumbnail generation");

            // Check if FFmpeg is available and set path
            try {
                const ffmpegStatic = require('ffmpeg-static');
                const ffmpegInstaller = require('@ffmpeg-installer/ffmpeg');

                let ffmpegPath = null;

                if (ffmpegStatic) {
                    ffmpegPath = ffmpegStatic;
                    console.log("📦 Using ffmpeg-static:", ffmpegPath);
                } else if (ffmpegInstaller && ffmpegInstaller.path) {
                    ffmpegPath = ffmpegInstaller.path;
                    console.log("📦 Using @ffmpeg-installer/ffmpeg:", ffmpegPath);
                } else if (process.env.FFMPEG_PATH) {
                    ffmpegPath = process.env.FFMPEG_PATH;
                    console.log("📦 Using custom FFmpeg path:", ffmpegPath);
                }

                if (!ffmpegPath) {
                    console.warn("⚠️ FFmpeg not found, skipping thumbnail generation");
                    cleanupTempFiles();
                    resolve(null);
                    return;
                }

                // Set FFmpeg path
                ffmpeg.setFfmpegPath(ffmpegPath);
                console.log("✅ FFmpeg configured successfully");

            } catch (error) {
                console.warn("⚠️ Error configuring FFmpeg:", error.message);
                cleanupTempFiles();
                resolve(null);
                return;
            }

            // Try multiple timestamp positions for thumbnail generation
            const timestamps = ['10%', '25%', '50%', '5s', '1s'];
            let thumbnailGenerated = false;

            for (const timestamp of timestamps) {
                if (thumbnailGenerated) break;

                try {
                    console.log(`🎬 Attempting thumbnail generation at ${timestamp}`);

                    await new Promise((resolveFFmpeg, rejectFFmpeg) => {
                        ffmpeg(tempVideoPath)
                            .screenshots({
                                timestamps: [timestamp],
                                filename: path.basename(tempThumbnailPath),
                                folder: path.dirname(tempThumbnailPath),
                                size: '640x360'
                            })
                            .on('end', () => {
                                if (fs.existsSync(tempThumbnailPath)) {
                                    thumbnailGenerated = true;
                                    resolveFFmpeg();
                                } else {
                                    rejectFFmpeg(new Error('Thumbnail file not created'));
                                }
                            })
                            .on('error', (error) => {
                                console.warn(`⚠️ Thumbnail generation failed at ${timestamp}:`, error.message);
                                rejectFFmpeg(error);
                            });
                    });

                    if (thumbnailGenerated) {
                        console.log(`✅ Thumbnail generated successfully at ${timestamp}`);
                        break;
                    }
                } catch (error) {
                    console.warn(`❌ Failed to generate thumbnail at ${timestamp}:`, error.message);
                    continue;
                }
            }

            if (thumbnailGenerated && fs.existsSync(tempThumbnailPath)) {
                // Read the generated thumbnail
                const thumbnailBuffer = fs.readFileSync(tempThumbnailPath);
                console.log(`📸 Thumbnail buffer size: ${(thumbnailBuffer.length / 1024).toFixed(2)} KB`);

                cleanupTempFiles();
                resolve(thumbnailBuffer);
            } else {
                console.warn("⚠️ All thumbnail generation attempts failed");
                cleanupTempFiles();
                resolve(null);
            }

        } catch (error) {
            console.error("❌ Error in thumbnail generation:", error.message);
            cleanupTempFiles();
            resolve(null); // Return null instead of rejecting to continue without thumbnail
        }

        function cleanupTempFiles() {
            try {
                if (fs.existsSync(tempVideoPath)) {
                    fs.unlinkSync(tempVideoPath);
                    console.log("🗑️ Cleaned up temporary video file");
                }
                if (fs.existsSync(tempThumbnailPath)) {
                    fs.unlinkSync(tempThumbnailPath);
                    console.log("🗑️ Cleaned up temporary thumbnail file");
                }
            } catch (cleanupError) {
                console.warn("⚠️ Failed to clean up temp files:", cleanupError.message);
            }
        }
    });
};

// Function to create a default thumbnail for videos without generated thumbnails
const createDefaultThumbnail = () => {
    // Return a base64 encoded default video thumbnail image
    const defaultThumbnailBase64 = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjMzMzIi8+Cjxwb2x5Z29uIHBvaW50cz0iMjUwLDE0MCAzNTAsMjAwIDI1MCwyNjAiIGZpbGw9IiNmZmYiLz4KPHRleHQgeD0iMzIwIiB5PSIzMDAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VmlkZW8gVGh1bWJuYWlsPC90ZXh0Pgo8L3N2Zz4=";
    return Buffer.from(defaultThumbnailBase64.split(',')[1], 'base64');
};

// Configure Multer Memory Storage with increased limits
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 500 * 1024 * 1024, // 500MB limit
    fieldSize: 500 * 1024 * 1024 // 500MB field size limit
  }
});

// Configure AWS S3 with optimized settings for large files
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
  httpOptions: {
    timeout: 600000, // 10 minutes for large files
    connectTimeout: 60000 // 1 minute
  },
  maxRetries: 5,
  retryDelayOptions: {
    customBackoff: function(retryCount) {
      return Math.pow(2, retryCount) * 1000; // Exponential backoff
    }
  },
  // Enable multipart uploads for files larger than 100MB
  s3ForcePathStyle: false,
  signatureVersion: 'v4',
  params: {
    Bucket: process.env.AWS_S3_BUCKET_NAME
  }
});

// Multipart upload function for large video files
const uploadLargeVideoToS3 = async (videoBuffer, filename, contentType, onProgress, makePublic = true) => {
  const fileSize = videoBuffer.length;
  const chunkSize = 100 * 1024 * 1024; // 100MB chunks

  // Use regular upload for files smaller than 100MB
  if (fileSize < chunkSize) {
    const params = {
      Bucket: process.env.AWS_S3_BUCKET_NAME,
      Key: filename,
      Body: videoBuffer,
      ContentType: contentType,
      ACL: makePublic ? 'public-read' : 'private', // Make videos publicly accessible
      ContentDisposition: 'inline', // Allow inline viewing in browser
      CacheControl: 'max-age=31536000', // Cache for 1 year
      Metadata: {
        'uploaded-by': 'stjoseph-admin',
        'content-type': contentType,
        'file-size': fileSize.toString()
      }
    };

    const upload = s3.upload(params);

    if (onProgress) {
      upload.on('httpUploadProgress', (progress) => {
        const percentCompleted = Math.round((progress.loaded * 100) / progress.total);
        onProgress(percentCompleted);
      });
    }

    return await upload.promise();
  }

  // Use multipart upload for large files
  const createParams = {
    Bucket: process.env.AWS_S3_BUCKET_NAME,
    Key: filename,
    ContentType: contentType,
    ACL: makePublic ? 'public-read' : 'private',
    CacheControl: 'max-age=31536000',
    Metadata: {
      'uploaded-by': 'stjoseph-admin',
      'content-type': contentType,
      'file-size': fileSize.toString()
    }
  };

  const multipart = await s3.createMultipartUpload(createParams).promise();
  const uploadId = multipart.UploadId;

  const parts = [];
  const numChunks = Math.ceil(fileSize / chunkSize);
  let uploadedBytes = 0;

  try {
    for (let i = 0; i < numChunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, fileSize);
      const chunk = videoBuffer.slice(start, end);

      const partParams = {
        Bucket: process.env.AWS_S3_BUCKET_NAME,
        Key: filename,
        PartNumber: i + 1,
        UploadId: uploadId,
        Body: chunk,
      };

      const partResult = await s3.uploadPart(partParams).promise();
      parts.push({
        ETag: partResult.ETag,
        PartNumber: i + 1,
      });

      uploadedBytes += chunk.length;
      if (onProgress) {
        const percentCompleted = Math.round((uploadedBytes * 100) / fileSize);
        onProgress(percentCompleted);
      }
    }

    const completeParams = {
      Bucket: process.env.AWS_S3_BUCKET_NAME,
      Key: filename,
      UploadId: uploadId,
      MultipartUpload: {
        Parts: parts,
      },
    };

    const result = await s3.completeMultipartUpload(completeParams).promise();
    return result;

  } catch (error) {
    // Abort multipart upload on error
    await s3.abortMultipartUpload({
      Bucket: process.env.AWS_S3_BUCKET_NAME,
      Key: filename,
      UploadId: uploadId,
    }).promise();
    throw error;
  }
};

// Fetch Study Content
router.post("/get-study-content", authMiddleware, async (req, res) => {
    const { content, className, subject } = req.body;
    const userId = req.body.userId;

    console.log(`🔍 Study content request: content=${content}, className=${className}, subject=${subject}, userId=${userId}`);

    if (content === "default" || subject === "default") {
        return res.status(400).send("Invalid Data!");
    }

    try {
        // Get user's level for access control
        const user = await User.findById(userId);

        if (!user) {
            return res.status(404).send("User not found");
        }

        // Use user's actual level for access control
        const userLevel = user.level;

        // Build base filter for level
        const baseFilter = {
            level: new RegExp(`^${userLevel}$`, 'i')
        };

        // Handle "all" subjects case with optimized query
        if (subject === "all") {
            // Add className filter if specified
            if (className && className !== "default" && className !== "all") {
                if (userLevel === 'secondary' || userLevel === 'advance') {
                    const normalizedClass = className.replace(/^Form-/, '');
                    baseFilter.$or = [
                        { className: className },
                        { className: normalizedClass },
                        { className: `Form-${normalizedClass}` }
                    ];
                } else {
                    baseFilter.className = className;
                }
            }

            // Fetch all materials for the user's level in one query
            let allMaterials = [];
            if (content === "study-notes") {
                allMaterials = await Notes.find(baseFilter);
            } else if (content === "past-papers") {
                allMaterials = await PastPapers.find(baseFilter);
            } else if (content === "videos") {
                allMaterials = await Videos.find(baseFilter);
            } else if (content === "books") {
                allMaterials = await Books.find(baseFilter);
            }

            return res.status(200).json({
                success: true,
                data: allMaterials.reverse(),
                message: allMaterials.length > 0 ? `${content} found` : `No ${content} available for this selection`
            });
        }

        // Build filter for specific subject
        const filter = {
            subject: new RegExp(`^${subject.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i'),
            level: new RegExp(`^${userLevel}$`, 'i')
        };

        // Only add className filter if it's specified and not default/all
        if (className && className !== "default" && className !== "all") {
            // Handle both "Form-X" and "X" formats for secondary and advance classes
            if (userLevel === 'secondary' || userLevel === 'advance') {
                const normalizedClass = className.replace(/^Form-/, '');
                filter.$or = [
                    { className: className },
                    { className: normalizedClass },
                    { className: `Form-${normalizedClass}` }
                ];
            } else {
                filter.className = className;
            }
        }

        if (content === "study-notes") {
            const notes = await Notes.find(filter);
            res.status(200).json({
                success: true,
                data: notes,
                message: notes.length > 0 ? "Notes found" : "No notes available for this selection"
            });
        }
        else if (content === "past-papers") {
            const papers = await PastPapers.find(filter);
            res.status(200).json({
                success: true,
                data: papers,
                message: papers.length > 0 ? "Papers found" : "No papers available for this selection"
            });
        }
        else if (content === "videos") {
            // Create a more flexible filter for videos
            const videoFilter = {
                subject: new RegExp(`^${subject.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i'),
                level: new RegExp(`^${userLevel}$`, 'i')
            };

            // Only add className filter if it's specified and not default/all
            if (className && className !== "default" && className !== "all") {
                // Handle both "Form-X" and "X" formats for secondary and advance classes
                if (userLevel === 'secondary' || userLevel === 'advance') {
                    const normalizedClass = className.replace(/^Form-/, '');
                    videoFilter.$or = [
                        // Videos where this class is the core class
                        { className: className },
                        { className: normalizedClass },
                        { className: `Form-${normalizedClass}` },
                        // Videos where this class is in additionalClasses
                        { additionalClasses: { $in: [className] } },
                        { additionalClasses: { $in: [normalizedClass] } },
                        { additionalClasses: { $in: [`Form-${normalizedClass}`] } }
                    ];
                } else {
                    videoFilter.$or = [
                        // Videos where this class is the core class
                        { className: className },
                        // Videos where this class is in additionalClasses
                        { additionalClasses: { $in: [className] } }
                    ];
                }
            }

            // Try the video-specific filter
            const videos = await Videos.find(videoFilter);

            // If no videos found with className, try without className restriction
            let fallbackVideos = [];
            if (videos.length === 0 && className && className !== "default" && className !== "all") {
                const fallbackFilter = {
                    subject: new RegExp(`^${subject.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i'),
                    level: new RegExp(`^${userLevel}$`, 'i')
                };
                fallbackVideos = await Videos.find(fallbackFilter);
            }

            // Use the best match available
            let allVideos = videos.length > 0 ? videos : fallbackVideos;

            // Deduplicate videos and add class context information
            const videoMap = new Map();
            const currentClass = className && className !== "default" && className !== "all" ? className : null;

            allVideos.forEach(video => {
                const videoKey = video._id.toString();

                if (!videoMap.has(videoKey)) {
                    // Determine if this is a core class video or shared video
                    let isCore = false;
                    let sharedFromClass = null;

                    if (currentClass) {
                        // Check if current class matches the video's core class
                        const normalizedCurrentClass = currentClass.replace(/^Form-/, '');
                        const normalizedVideoClass = video.className.replace(/^Form-/, '');

                        if (normalizedCurrentClass === normalizedVideoClass ||
                            currentClass === video.className ||
                            (userLevel === 'secondary' || userLevel === 'advance') &&
                            (currentClass === `Form-${normalizedVideoClass}` ||
                             normalizedCurrentClass === normalizedVideoClass)) {
                            // This is the core class for this video
                            isCore = true;
                        } else if (video.additionalClasses && video.additionalClasses.length > 0) {
                            // Check if current class is in the additionalClasses array
                            const isInAdditionalClasses = video.additionalClasses.some(additionalClass => {
                                const normalizedAdditionalClass = additionalClass.replace(/^Form-/, '');
                                return normalizedCurrentClass === normalizedAdditionalClass ||
                                       currentClass === additionalClass ||
                                       (userLevel === 'secondary' || userLevel === 'advance') &&
                                       (currentClass === `Form-${normalizedAdditionalClass}` ||
                                        normalizedCurrentClass === normalizedAdditionalClass);
                            });

                            if (isInAdditionalClasses) {
                                // This video is shared TO the current class FROM the core class
                                isCore = false;
                                sharedFromClass = video.className;
                            }
                        }
                    } else {
                        // If no specific class is selected, treat all as core
                        isCore = true;
                    }

                    // Add class context to video object
                    const videoWithContext = {
                        ...video.toObject(),
                        isCore,
                        sharedFromClass,
                        coreClass: video.className
                    };

                    // Debug logging (can be removed in production)
                    if (process.env.NODE_ENV === 'development') {
                        console.log(`📹 Video "${video.title}" for class ${currentClass}:`, {
                            coreClass: video.className,
                            additionalClasses: video.additionalClasses,
                            isCore,
                            sharedFromClass
                        });
                    }

                    videoMap.set(videoKey, videoWithContext);
                }
            });

            const finalVideos = Array.from(videoMap.values());

            console.log(`📹 Returning ${finalVideos.length} deduplicated videos for ${subject}`);

            res.status(200).json({
                success: true,
                data: finalVideos.reverse(), // Reverse the order here
                message: finalVideos.length > 0 ? "Videos found" : "No videos available for this selection"
            });
        }
        else if (content === "books") {
            const books = await Books.find(filter);
            res.status(200).json({
                success: true,
                data: books,
                message: books.length > 0 ? "Books found" : "No books available for this selection"
            });
        }
        else {
            res.status(400).send("Invalid Data!");
        }
    } catch (error) {
        console.log("Error while fetching study content: ", error);
        res.status(500).send("Internal Server Error");
    }
});

// Add Study Material Endpoints

// Add Video (supports both YouTube links and file uploads)
router.post("/add-video", authMiddleware, upload.fields([
    { name: "video", maxCount: 1 },
    { name: "thumbnail", maxCount: 1 }
]), async (req, res) => {
    try {
        const { className, subject, title, level, videoID, videoUrl, thumbnailUrl, additionalClasses } = req.body;
        const files = req.files;

        // Parse additionalClasses if it's a string (from FormData)
        let parsedAdditionalClasses = [];
        if (additionalClasses) {
            if (Array.isArray(additionalClasses)) {
                parsedAdditionalClasses = additionalClasses;
            } else if (typeof additionalClasses === 'string') {
                try {
                    // Try to parse as JSON first
                    parsedAdditionalClasses = JSON.parse(additionalClasses);
                } catch {
                    // If not JSON, treat as comma-separated string or single value
                    parsedAdditionalClasses = additionalClasses.split(',').map(cls => cls.trim()).filter(cls => cls);
                }
            }
        }

        // Validate required fields
        if (!className || !subject || !title || !level) {
            return res.status(400).json({
                success: false,
                message: "Missing required fields: className, subject, title, and level are required"
            });
        }

        // Check if either videoID, videoUrl (S3), or video file is provided
        if (!videoID && !videoUrl && (!files || !files.video)) {
            return res.status(400).json({
                success: false,
                message: "Either videoID (for YouTube), videoUrl (for S3), or video file must be provided"
            });
        }

        let finalVideoID = videoID || "";
        let finalVideoUrl = videoUrl || "";
        let finalThumbnail = thumbnailUrl || "";

        // For file uploads, upload immediately and wait for completion
        if (files && files.video && files.video[0]) {
            try {
                const videoFile = files.video[0];
                const folderName = "StudyMaterials/Videos";
                const videoFilename = `${folderName}/${uuidv4()}-${videoFile.originalname}`;

                console.log("📤 Uploading video to S3...");
                console.log(`📊 Video size: ${(videoFile.buffer.length / (1024 * 1024)).toFixed(2)} MB`);

                // Use optimized multipart upload for large files with public access
                const s3Response = await uploadLargeVideoToS3(
                    videoFile.buffer,
                    videoFilename,
                    videoFile.mimetype || "video/mp4",
                    (progress) => {
                        console.log(`📈 Upload progress: ${progress}%`);
                    },
                    true // Make video publicly accessible
                );

                finalVideoUrl = s3Response.Location;
                finalVideoID = s3Response.Location; // Use S3 URL as videoID for uploaded files
                console.log("✅ Video uploaded:", s3Response.Location);

                // Generate thumbnail if no custom thumbnail provided
                if (!files.thumbnail && !thumbnailUrl) {
                    try {
                        console.log("🎬 Generating thumbnail from video...");
                        const thumbnailBuffer = await generateVideoThumbnail(videoFile.buffer, videoFilename);

                        if (thumbnailBuffer) {
                            const thumbnailFilename = `StudyMaterials/Thumbnails/${uuidv4()}-thumbnail.jpg`;
                            const thumbnailParams = {
                                Bucket: process.env.AWS_S3_BUCKET_NAME,
                                Key: thumbnailFilename,
                                Body: thumbnailBuffer,
                                ContentType: "image/jpeg",
                            };

                            const thumbnailS3Response = await s3.upload(thumbnailParams).promise();
                            finalThumbnail = thumbnailS3Response.Location;
                            console.log("✅ Thumbnail generated and uploaded:", thumbnailS3Response.Location);
                        } else {
                            console.log("⚠️ Thumbnail generation failed, using default thumbnail");
                            // Upload default thumbnail
                            const defaultThumbnailBuffer = createDefaultThumbnail();
                            const defaultThumbnailFilename = `StudyMaterials/Thumbnails/${uuidv4()}-default-thumbnail.jpg`;
                            const defaultThumbnailParams = {
                                Bucket: process.env.AWS_S3_BUCKET_NAME,
                                Key: defaultThumbnailFilename,
                                Body: defaultThumbnailBuffer,
                                ContentType: "image/jpeg",
                            };

                            const defaultThumbnailS3Response = await s3.upload(defaultThumbnailParams).promise();
                            finalThumbnail = defaultThumbnailS3Response.Location;
                            console.log("✅ Default thumbnail uploaded:", defaultThumbnailS3Response.Location);
                        }
                    } catch (thumbnailError) {
                        console.error("❌ Failed to generate thumbnail:", thumbnailError.message);
                        // Don't fail the entire upload if thumbnail generation fails
                        finalThumbnail = ""; // Set empty string instead of "processing"
                    }
                }
            } catch (videoError) {
                console.error("❌ Failed to upload video:", videoError.message);
                return res.status(500).json({
                    success: false,
                    message: "Failed to upload video to S3",
                    error: videoError.message
                });
            }
        } else if (videoUrl && !videoID) {
            // Handle S3 URL method - use the provided S3 URL directly
            console.log("🔗 Using S3 object URL:", videoUrl);
            finalVideoUrl = videoUrl;
            finalVideoID = videoUrl; // Use S3 URL as videoID for S3 videos
        }

        // Upload custom thumbnail if provided
        if (files && files.thumbnail && files.thumbnail[0]) {
            try {
                const thumbnailFile = files.thumbnail[0];
                const thumbnailFilename = `StudyMaterials/Thumbnails/${uuidv4()}-${thumbnailFile.originalname}`;

                const thumbnailParams = {
                    Bucket: process.env.AWS_S3_BUCKET_NAME,
                    Key: thumbnailFilename,
                    Body: thumbnailFile.buffer,
                    ContentType: thumbnailFile.mimetype || "image/jpeg",
                };

                console.log("📤 Uploading custom thumbnail to S3...");
                const thumbnailS3Response = await s3.upload(thumbnailParams).promise();
                finalThumbnail = thumbnailS3Response.Location;
                console.log("✅ Custom thumbnail uploaded:", thumbnailS3Response.Location);
            } catch (thumbnailError) {
                console.error("❌ Failed to upload custom thumbnail:", thumbnailError.message);
                finalThumbnail = ""; // Set empty string instead of "processing"
            }
        }

        // Create video document with optimized save operation
        const videoData = {
            className,
            additionalClasses: parsedAdditionalClasses,
            subject,
            title,
            level,
            videoID: finalVideoID,
            videoUrl: finalVideoUrl,
            thumbnail: finalThumbnail
        };

        console.log("💾 Saving video to database...");
        const startTime = Date.now();

        // Use insertOne for better performance with large documents
        const savedVideo = await Videos.create(videoData);

        const saveTime = Date.now() - startTime;
        console.log(`✅ Video saved to database in ${saveTime}ms:`, {
            id: savedVideo._id,
            title: savedVideo.title,
            videoUrl: savedVideo.videoUrl ? 'Set' : 'Not set',
            thumbnail: savedVideo.thumbnail ? 'Set' : 'Not set'
        });

        // Generate subtitles automatically for uploaded videos (async, don't wait)
        if (finalVideoUrl && (finalVideoUrl.includes('amazonaws.com') || finalVideoUrl.includes('s3.'))) {
            console.log("🎬 Starting automatic subtitle generation...");

            // Update subtitle generation status
            Videos.findByIdAndUpdate(savedVideo._id, {
                subtitleGenerationStatus: "processing"
            }).catch(err => console.warn("Failed to update subtitle status:", err.message));

            // Generate subtitles asynchronously (don't block response)
            subtitleService.generateSubtitlesFromS3Video(finalVideoUrl, savedVideo._id.toString(), 'en')
                .then(async (subtitleData) => {
                    try {
                        await Videos.findByIdAndUpdate(savedVideo._id, {
                            $push: { subtitles: subtitleData },
                            hasSubtitles: true,
                            subtitleGenerationStatus: "completed"
                        });
                        console.log(`✅ Auto-generated subtitles for video: ${savedVideo._id}`);
                    } catch (updateError) {
                        console.error("Failed to save auto-generated subtitles:", updateError.message);
                        await Videos.findByIdAndUpdate(savedVideo._id, {
                            subtitleGenerationStatus: "failed"
                        }).catch(() => {});
                    }
                })
                .catch(async (subtitleError) => {
                    console.warn("Auto subtitle generation failed:", subtitleError.message);
                    await Videos.findByIdAndUpdate(savedVideo._id, {
                        subtitleGenerationStatus: "failed"
                    }).catch(() => {});
                });
        }

        // Send response with the complete video data
        res.status(201).json({
            success: true,
            data: savedVideo,
            message: "Video added successfully"
        });
    } catch (error) {
        console.error("Error adding video:", error);
        res.status(500).json({
            success: false,
            message: "Failed to add video",
            error: error.message
        });
    }
});

// Add Note
router.post("/add-note", authMiddleware, upload.single("document"), async (req, res) => {
    try {
        const { className, subject, title, level } = req.body;
        const documentFile = req.file;

        // Validate required fields
        if (!className || !subject || !title || !level) {
            return res.status(400).json({
                success: false,
                message: "Missing required fields: className, subject, title, and level are required"
            });
        }

        let documentID = "";
        let documentUrl = "";

        // Handle file upload if present
        if (documentFile) {
            const folderName = "StudyMaterials/Notes";
            const filename = `${folderName}/${uuidv4()}-${documentFile.originalname}`;

            const params = {
                Bucket: process.env.AWS_S3_BUCKET_NAME,
                Key: filename,
                Body: documentFile.buffer,
                ContentType: documentFile.mimetype || "application/octet-stream",
            };

            const s3Response = await s3.upload(params).promise();
            documentUrl = s3Response.Location;
            documentID = s3Response.Key;
        }

        const newNote = new Notes({
            className,
            subject,
            title,
            level,
            documentID,
            documentUrl
        });

        const savedNote = await newNote.save();

        res.status(201).json({
            success: true,
            data: savedNote,
            message: "Note added successfully"
        });
    } catch (error) {
        console.error("Error adding note:", error);
        res.status(500).json({
            success: false,
            message: "Failed to add note",
            error: error.message
        });
    }
});

// Add Past Paper
router.post("/add-past-paper", authMiddleware, upload.single("document"), async (req, res) => {
    try {
        const { className, subject, title, level, year } = req.body;
        const documentFile = req.file;

        // Validate required fields
        if (!className || !subject || !title || !level || !year) {
            return res.status(400).json({
                success: false,
                message: "Missing required fields: className, subject, title, level, and year are required"
            });
        }

        let documentID = "";
        let documentUrl = "";

        // Handle file upload if present
        if (documentFile) {
            const folderName = "StudyMaterials/PastPapers";
            const filename = `${folderName}/${uuidv4()}-${documentFile.originalname}`;

            const params = {
                Bucket: process.env.AWS_S3_BUCKET_NAME,
                Key: filename,
                Body: documentFile.buffer,
                ContentType: documentFile.mimetype || "application/octet-stream",
            };

            const s3Response = await s3.upload(params).promise();
            documentUrl = s3Response.Location;
            documentID = s3Response.Key;
        }

        const newPastPaper = new PastPapers({
            className,
            subject,
            title,
            level,
            year,
            documentID,
            documentUrl
        });

        const savedPastPaper = await newPastPaper.save();

        res.status(201).json({
            success: true,
            data: savedPastPaper,
            message: "Past paper added successfully"
        });
    } catch (error) {
        console.error("Error adding past paper:", error);
        res.status(500).json({
            success: false,
            message: "Failed to add past paper",
            error: error.message
        });
    }
});

// Add Book
router.post("/add-book", authMiddleware, upload.fields([
    { name: "document", maxCount: 1 },
    { name: "thumbnail", maxCount: 1 }
]), async (req, res) => {
    try {
        const { className, subject, title, level, year } = req.body;
        const files = req.files;

        // Validate required fields
        if (!className || !subject || !title || !level || !year) {
            return res.status(400).json({
                success: false,
                message: "Missing required fields: className, subject, title, level, and year are required"
            });
        }

        let documentID = "";
        let documentUrl = "";
        let thumbnailUrl = "";

        // Handle document upload
        if (files && files.document && files.document[0]) {
            const documentFile = files.document[0];
            const folderName = "StudyMaterials/Books";
            const filename = `${folderName}/${uuidv4()}-${documentFile.originalname}`;

            const params = {
                Bucket: process.env.AWS_S3_BUCKET_NAME,
                Key: filename,
                Body: documentFile.buffer,
                ContentType: documentFile.mimetype || "application/octet-stream",
            };

            const s3Response = await s3.upload(params).promise();
            documentUrl = s3Response.Location;
            documentID = s3Response.Key;
        }

        // Handle thumbnail upload
        if (files && files.thumbnail && files.thumbnail[0]) {
            const thumbnailFile = files.thumbnail[0];
            const folderName = "StudyMaterials/Thumbnails";
            const filename = `${folderName}/${uuidv4()}-${thumbnailFile.originalname}`;

            const params = {
                Bucket: process.env.AWS_S3_BUCKET_NAME,
                Key: filename,
                Body: thumbnailFile.buffer,
                ContentType: thumbnailFile.mimetype || "application/octet-stream",
            };

            const s3Response = await s3.upload(params).promise();
            thumbnailUrl = s3Response.Location;
        }

        const newBook = new Books({
            className,
            subject,
            title,
            level,
            year,
            documentID,
            documentUrl,
            thumbnail: thumbnailUrl
        });

        const savedBook = await newBook.save();

        res.status(201).json({
            success: true,
            data: savedBook,
            message: "Book added successfully"
        });
    } catch (error) {
        console.error("Error adding book:", error);
        res.status(500).json({
            success: false,
            message: "Failed to add book",
            error: error.message
        });
    }
});


//Seed For Study Material

const saveNote = async () => {
    const Note = new Notes({
        className: '4',
        subject: 'Religion',
        title: 'r_test',
        level : "primary",
        documentID: '1ae3f2diAVCP4ipqDc-4nvbu4u51aDvZE'
    });
    const savedNote = await Note.save();
    if (savedNote) {
        console.log('Note Saved');
    }
}

// saveNote();

const savePastPaper = async () => {
    const Paper = new PastPapers({
        className: '4',
        subject: 'English',
        title: 'resume',
        year: '2023',
        level : "primary",

        documentID: '1BECPvz_RopF184M962FSLsM-cyc7uyYy'
    });
    const savedPaper = await Paper.save();
    if (savedPaper) {
        console.log('Paper Saved');
    }
}

// savePastPaper();

const saveVideo = async () => {
    const Video = new Videos({
        className: '4',
        subject: 'Science',
        title: 'f_s_test2',
        level : "primary",

        videoID: 'H1YR5rsScC8'
    });
    const savedVideo = await Video.save();
    if (savedVideo) {
        console.log('Video Saved');
    }
}

// Function to fix missing videoID fields
const fixMissingVideoIDs = async () => {
    try {
        // Sample YouTube video IDs for testing
        const sampleVideoIDs = [
            'dQw4w9WgXcQ', // Rick Roll (safe for testing)
            'jNQXAC9IVRw', // Me at the zoo (first YouTube video)
            'kJQP7kiw5Fk', // Despacito
            'fJ9rUzIMcZQ', // Gangnam Style
            'YQHsXMglC9A', // Hello by Adele
            'hTWKbfoikeg', // Smells Like Teen Spirit
            'QH2-TGUlwu4', // Nyan Cat
            'astISOttCQ0', // Chocolate Rain
            'oHg5SJYRHA0', // RickRoll
            'L_jWHffIx5E'  // Smash Mouth - All Star
        ];

        // Find videos without videoID
        const videosWithoutID = await Videos.find({
            $or: [
                { videoID: { $exists: false } },
                { videoID: null },
                { videoID: "" }
            ]
        });

        console.log(`Found ${videosWithoutID.length} videos without videoID`);

        // Update each video with a sample videoID
        for (let i = 0; i < videosWithoutID.length; i++) {
            const video = videosWithoutID[i];
            const videoID = sampleVideoIDs[i % sampleVideoIDs.length];

            await Videos.findByIdAndUpdate(video._id, { videoID: videoID });
            console.log(`Updated video "${video.title}" with videoID: ${videoID}`);
        }

        console.log('✅ All videos now have videoID fields');
    } catch (error) {
        console.error('Error fixing videoIDs:', error);
    }
};

// Uncomment the line below to run the fix
// fixMissingVideoIDs();

// saveVideo();

const saveBook = async () => {
    const Book = new Books({
        className: '4',
        subject: 'English',
        title: 'Metaverse',
        year: '2021',
        level : "primary",

        documentID: '1IvPoEFJzkplW7_BmWnB7iAJZ_5w1kfzH',
        thumbnail: 'https://picsum.photos/id/11/250',
    });
    const savedBook = await Book.save();
    if (savedBook) {
        console.log('Book Saved');
    }
}

// saveBook();

// Get all available classes for user's level
// Video proxy endpoint to handle CORS issues
router.get("/video-proxy", async (req, res) => {
    try {
        const { url } = req.query;

        if (!url) {
            return res.status(400).json({ error: "Video URL is required" });
        }

        console.log("🎥 Proxying video request for:", url);

        // Set appropriate headers for video streaming
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Range, Content-Type');
        res.setHeader('Accept-Ranges', 'bytes');

        // Import axios for making the request
        const axios = require('axios');

        // Forward the request to the actual video URL
        const videoResponse = await axios({
            method: 'GET',
            url: url,
            responseType: 'stream',
            headers: {
                'Range': req.headers.range || '',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        // Forward response headers
        res.setHeader('Content-Type', videoResponse.headers['content-type'] || 'video/mp4');
        res.setHeader('Content-Length', videoResponse.headers['content-length'] || '');

        if (videoResponse.headers['content-range']) {
            res.setHeader('Content-Range', videoResponse.headers['content-range']);
            res.status(206); // Partial Content
        }

        // Pipe the video stream
        videoResponse.data.pipe(res);

    } catch (error) {
        console.error("❌ Video proxy error:", error.message);
        res.status(500).json({ error: "Failed to proxy video", details: error.message });
    }
});

// Clean up videos with processing status
router.post("/cleanup-processing-videos", authMiddleware, async (req, res) => {
    try {
        const processingVideos = await Videos.find({
            $or: [
                { videoUrl: "processing" },
                { thumbnail: "processing" }
            ]
        });

        console.log(`🧹 Found ${processingVideos.length} videos with processing status`);

        if (processingVideos.length > 0) {
            // Remove videos that are stuck in processing
            await Videos.deleteMany({
                $or: [
                    { videoUrl: "processing" },
                    { thumbnail: "processing" }
                ]
            });

            console.log(`✅ Cleaned up ${processingVideos.length} processing videos`);
        }

        res.status(200).json({
            success: true,
            message: `Cleaned up ${processingVideos.length} processing videos`,
            count: processingVideos.length
        });
    } catch (error) {
        console.error("Error cleaning up processing videos:", error);
        res.status(500).json({
            success: false,
            message: "Failed to cleanup processing videos",
            error: error.message
        });
    }
});

// Update Study Material Endpoints

// Update Video
router.put("/update-video/:id", authMiddleware, upload.fields([
    { name: "thumbnail", maxCount: 1 }
]), async (req, res) => {
    try {
        const { id } = req.params;
        const { className, subject, title, level, videoID, videoUrl, thumbnailUrl, additionalClasses } = req.body;
        const files = req.files;

        // Find the existing video
        const existingVideo = await Videos.findById(id);
        if (!existingVideo) {
            return res.status(404).json({
                success: false,
                message: "Video not found"
            });
        }

        // Validate required fields
        if (!className || !subject || !title || !level) {
            return res.status(400).json({
                success: false,
                message: "Missing required fields: className, subject, title, and level are required"
            });
        }

        let updateData = {
            className,
            subject,
            title,
            level,
            additionalClasses: additionalClasses ? JSON.parse(additionalClasses) : []
        };

        // Handle video URL updates
        if (videoID) {
            updateData.videoID = videoID;
        }
        if (videoUrl) {
            updateData.videoUrl = videoUrl;
        }

        // Handle thumbnail upload if provided
        if (files && files.thumbnail && files.thumbnail[0]) {
            const thumbnailFile = files.thumbnail[0];
            const folderName = "StudyMaterials/Thumbnails";
            const filename = `${folderName}/${uuidv4()}-${thumbnailFile.originalname}`;

            const params = {
                Bucket: process.env.AWS_S3_BUCKET_NAME,
                Key: filename,
                Body: thumbnailFile.buffer,
                ContentType: thumbnailFile.mimetype || "application/octet-stream",
            };

            const s3Response = await s3.upload(params).promise();
            updateData.thumbnail = s3Response.Location;
        } else if (thumbnailUrl) {
            updateData.thumbnail = thumbnailUrl;
        }

        const updatedVideo = await Videos.findByIdAndUpdate(id, updateData, { new: true });

        res.status(200).json({
            success: true,
            data: updatedVideo,
            message: "Video updated successfully"
        });
    } catch (error) {
        console.error("Error updating video:", error);
        res.status(500).json({
            success: false,
            message: "Failed to update video",
            error: error.message
        });
    }
});

// Update Note
router.put("/update-note/:id", authMiddleware, upload.single("document"), async (req, res) => {
    try {
        const { id } = req.params;
        const { className, subject, title, level } = req.body;
        const documentFile = req.file;

        // Find the existing note
        const existingNote = await Notes.findById(id);
        if (!existingNote) {
            return res.status(404).json({
                success: false,
                message: "Note not found"
            });
        }

        // Validate required fields
        if (!className || !subject || !title || !level) {
            return res.status(400).json({
                success: false,
                message: "Missing required fields: className, subject, title, and level are required"
            });
        }

        let updateData = {
            className,
            subject,
            title,
            level
        };

        // Handle document upload if provided
        if (documentFile) {
            const folderName = "StudyMaterials/Notes";
            const filename = `${folderName}/${uuidv4()}-${documentFile.originalname}`;

            const params = {
                Bucket: process.env.AWS_S3_BUCKET_NAME,
                Key: filename,
                Body: documentFile.buffer,
                ContentType: documentFile.mimetype || "application/octet-stream",
            };

            const s3Response = await s3.upload(params).promise();
            updateData.documentUrl = s3Response.Location;
            updateData.documentID = s3Response.Key;
        }

        const updatedNote = await Notes.findByIdAndUpdate(id, updateData, { new: true });

        res.status(200).json({
            success: true,
            data: updatedNote,
            message: "Note updated successfully"
        });
    } catch (error) {
        console.error("Error updating note:", error);
        res.status(500).json({
            success: false,
            message: "Failed to update note",
            error: error.message
        });
    }
});

// Update Past Paper
router.put("/update-past-paper/:id", authMiddleware, upload.single("document"), async (req, res) => {
    try {
        const { id } = req.params;
        const { className, subject, title, level, year } = req.body;
        const documentFile = req.file;

        // Find the existing past paper
        const existingPaper = await PastPapers.findById(id);
        if (!existingPaper) {
            return res.status(404).json({
                success: false,
                message: "Past paper not found"
            });
        }

        // Validate required fields
        if (!className || !subject || !title || !level || !year) {
            return res.status(400).json({
                success: false,
                message: "Missing required fields: className, subject, title, level, and year are required"
            });
        }

        let updateData = {
            className,
            subject,
            title,
            level,
            year
        };

        // Handle document upload if provided
        if (documentFile) {
            const folderName = "StudyMaterials/PastPapers";
            const filename = `${folderName}/${uuidv4()}-${documentFile.originalname}`;

            const params = {
                Bucket: process.env.AWS_S3_BUCKET_NAME,
                Key: filename,
                Body: documentFile.buffer,
                ContentType: documentFile.mimetype || "application/octet-stream",
            };

            const s3Response = await s3.upload(params).promise();
            updateData.documentUrl = s3Response.Location;
            updateData.documentID = s3Response.Key;
        }

        const updatedPaper = await PastPapers.findByIdAndUpdate(id, updateData, { new: true });

        res.status(200).json({
            success: true,
            data: updatedPaper,
            message: "Past paper updated successfully"
        });
    } catch (error) {
        console.error("Error updating past paper:", error);
        res.status(500).json({
            success: false,
            message: "Failed to update past paper",
            error: error.message
        });
    }
});

// Update Book
router.put("/update-book/:id", authMiddleware, upload.fields([
    { name: "document", maxCount: 1 },
    { name: "thumbnail", maxCount: 1 }
]), async (req, res) => {
    try {
        const { id } = req.params;
        const { className, subject, title, level, year } = req.body;
        const files = req.files;

        // Find the existing book
        const existingBook = await Books.findById(id);
        if (!existingBook) {
            return res.status(404).json({
                success: false,
                message: "Book not found"
            });
        }

        // Validate required fields
        if (!className || !subject || !title || !level || !year) {
            return res.status(400).json({
                success: false,
                message: "Missing required fields: className, subject, title, level, and year are required"
            });
        }

        let updateData = {
            className,
            subject,
            title,
            level,
            year
        };

        // Handle document upload if provided
        if (files && files.document && files.document[0]) {
            const documentFile = files.document[0];
            const folderName = "StudyMaterials/Books";
            const filename = `${folderName}/${uuidv4()}-${documentFile.originalname}`;

            const params = {
                Bucket: process.env.AWS_S3_BUCKET_NAME,
                Key: filename,
                Body: documentFile.buffer,
                ContentType: documentFile.mimetype || "application/octet-stream",
            };

            const s3Response = await s3.upload(params).promise();
            updateData.documentUrl = s3Response.Location;
            updateData.documentID = s3Response.Key;
        }

        // Handle thumbnail upload if provided
        if (files && files.thumbnail && files.thumbnail[0]) {
            const thumbnailFile = files.thumbnail[0];
            const folderName = "StudyMaterials/Thumbnails";
            const filename = `${folderName}/${uuidv4()}-${thumbnailFile.originalname}`;

            const params = {
                Bucket: process.env.AWS_S3_BUCKET_NAME,
                Key: filename,
                Body: thumbnailFile.buffer,
                ContentType: thumbnailFile.mimetype || "application/octet-stream",
            };

            const s3Response = await s3.upload(params).promise();
            updateData.thumbnail = s3Response.Location;
        }

        const updatedBook = await Books.findByIdAndUpdate(id, updateData, { new: true });

        res.status(200).json({
            success: true,
            data: updatedBook,
            message: "Book updated successfully"
        });
    } catch (error) {
        console.error("Error updating book:", error);
        res.status(500).json({
            success: false,
            message: "Failed to update book",
            error: error.message
        });
    }
});

// Delete Study Material Endpoints

// Delete Video
router.delete("/delete-video/:id", authMiddleware, async (req, res) => {
    try {
        const { id } = req.params;

        const deletedVideo = await Videos.findByIdAndDelete(id);
        if (!deletedVideo) {
            return res.status(404).json({
                success: false,
                message: "Video not found"
            });
        }

        res.status(200).json({
            success: true,
            message: "Video deleted successfully"
        });
    } catch (error) {
        console.error("Error deleting video:", error);
        res.status(500).json({
            success: false,
            message: "Failed to delete video",
            error: error.message
        });
    }
});

// Delete Note
router.delete("/delete-note/:id", authMiddleware, async (req, res) => {
    try {
        const { id } = req.params;

        const deletedNote = await Notes.findByIdAndDelete(id);
        if (!deletedNote) {
            return res.status(404).json({
                success: false,
                message: "Note not found"
            });
        }

        res.status(200).json({
            success: true,
            message: "Note deleted successfully"
        });
    } catch (error) {
        console.error("Error deleting note:", error);
        res.status(500).json({
            success: false,
            message: "Failed to delete note",
            error: error.message
        });
    }
});

// Delete Past Paper
router.delete("/delete-past-paper/:id", authMiddleware, async (req, res) => {
    try {
        const { id } = req.params;

        const deletedPaper = await PastPapers.findByIdAndDelete(id);
        if (!deletedPaper) {
            return res.status(404).json({
                success: false,
                message: "Past paper not found"
            });
        }

        res.status(200).json({
            success: true,
            message: "Past paper deleted successfully"
        });
    } catch (error) {
        console.error("Error deleting past paper:", error);
        res.status(500).json({
            success: false,
            message: "Failed to delete past paper",
            error: error.message
        });
    }
});

// Delete Book
router.delete("/delete-book/:id", authMiddleware, async (req, res) => {
    try {
        const { id } = req.params;

        const deletedBook = await Books.findByIdAndDelete(id);
        if (!deletedBook) {
            return res.status(404).json({
                success: false,
                message: "Book not found"
            });
        }

        res.status(200).json({
            success: true,
            message: "Book deleted successfully"
        });
    } catch (error) {
        console.error("Error deleting book:", error);
        res.status(500).json({
            success: false,
            message: "Failed to delete book",
            error: error.message
        });
    }
});

// Get all study materials for admin management
router.get("/admin/all-materials", authMiddleware, async (req, res) => {
    try {
        const { materialType, level, className, subject } = req.query;

        let filter = {};
        if (level) filter.level = level;
        if (className) filter.className = className;
        if (subject) filter.subject = subject;

        let materials = [];

        if (!materialType || materialType === "videos") {
            const videos = await Videos.find(filter).sort({ _id: -1 });
            materials = materials.concat(videos.map(video => ({ ...video.toObject(), type: "videos" })));
        }

        if (!materialType || materialType === "study-notes") {
            const notes = await Notes.find(filter).sort({ _id: -1 });
            materials = materials.concat(notes.map(note => ({ ...note.toObject(), type: "study-notes" })));
        }

        if (!materialType || materialType === "past-papers") {
            const papers = await PastPapers.find(filter).sort({ _id: -1 });
            materials = materials.concat(papers.map(paper => ({ ...paper.toObject(), type: "past-papers" })));
        }

        if (!materialType || materialType === "books") {
            const books = await Books.find(filter).sort({ _id: -1 });
            materials = materials.concat(books.map(book => ({ ...book.toObject(), type: "books" })));
        }

        // Sort by creation date (newest first)
        materials.sort((a, b) => new Date(b._id.getTimestamp()) - new Date(a._id.getTimestamp()));

        res.status(200).json({
            success: true,
            data: materials,
            message: `Found ${materials.length} study materials`
        });
    } catch (error) {
        console.error("Error fetching all study materials:", error);
        res.status(500).json({
            success: false,
            message: "Failed to fetch study materials",
            error: error.message
        });
    }
});

router.post("/get-available-classes", authMiddleware, async (req, res) => {
    const userId = req.body.userId;

    try {
        // Get user's level
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).send("User not found");
        }

        const userLevel = user.level;

        // Get distinct classes available for this level across all study materials
        const notesClasses = await Notes.distinct("className", { level: userLevel });
        const papersClasses = await PastPapers.distinct("className", { level: userLevel });
        const videosClasses = await Videos.distinct("className", { level: userLevel });
        const booksClasses = await Books.distinct("className", { level: userLevel });

        // Combine and deduplicate all classes
        const allClasses = [...new Set([...notesClasses, ...papersClasses, ...videosClasses, ...booksClasses])];

        res.status(200).json({
            success: true,
            data: allClasses.sort(),
            userLevel: userLevel
        });
    } catch (error) {
        console.log(error);
        res.status(500).send({
            message: error.message,
            success: false,
        });
    }
});

// Handle preflight requests for video endpoints
router.options("/video-signed-url", (req, res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization, Accept, Origin, X-Requested-With');
    res.setHeader('Access-Control-Expose-Headers', 'Content-Range, Accept-Ranges, Content-Length, Content-Type');
    res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
    res.status(200).end();
});

router.options("/video-proxy", (req, res) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization, Accept, Origin, X-Requested-With');
    res.setHeader('Access-Control-Expose-Headers', 'Content-Range, Accept-Ranges, Content-Length, Content-Type');
    res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
    res.status(200).end();
});

// Generate signed URL for S3 videos
router.get("/video-signed-url", async (req, res) => {
    try {
        const { videoUrl } = req.query;

        if (!videoUrl) {
            return res.status(400).json({ success: false, message: "Video URL is required" });
        }

        // Set enhanced CORS headers for video requests
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization, Accept, Origin, X-Requested-With');
        res.setHeader('Access-Control-Expose-Headers', 'Content-Range, Accept-Ranges, Content-Length, Content-Type');

        // Check if it's an S3 URL
        if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {
            try {
                // Extract the S3 key from the URL - improved parsing
                let key;
                if (videoUrl.includes('amazonaws.com')) {
                    // Handle standard S3 URLs: https://bucket.s3.region.amazonaws.com/key
                    const urlObj = new URL(videoUrl);
                    key = decodeURIComponent(urlObj.pathname.substring(1)); // Remove leading slash and decode
                } else {
                    // Handle s3:// URLs or other formats
                    const urlParts = videoUrl.split('/');
                    const bucketIndex = urlParts.findIndex(part => part.includes('amazonaws.com') || part.includes('s3.'));
                    key = decodeURIComponent(urlParts.slice(bucketIndex + 1).join('/'));
                }

                if (!key) {
                    console.warn("⚠️ Could not extract S3 key from URL:", videoUrl);
                    return res.json({ success: true, signedUrl: videoUrl });
                }

                console.log("🔍 Extracted S3 key:", key);

                // First, try to check if object exists and is accessible
                try {
                    await s3.headObject({
                        Bucket: process.env.AWS_S3_BUCKET_NAME,
                        Key: key
                    }).promise();
                    console.log("✅ Object exists and is accessible");
                } catch (headError) {
                    console.warn("⚠️ Object not accessible:", headError.message);
                    // Try to make the object public
                    try {
                        await s3.putObjectAcl({
                            Bucket: process.env.AWS_S3_BUCKET_NAME,
                            Key: key,
                            ACL: 'public-read'
                        }).promise();
                        console.log("✅ Made object public");
                    } catch (aclError) {
                        console.warn("⚠️ Could not make object public:", aclError.message);
                    }
                }

                // Generate signed URL valid for 24 hours with proper content type
                const signedUrl = s3.getSignedUrl('getObject', {
                    Bucket: process.env.AWS_S3_BUCKET_NAME,
                    Key: key,
                    Expires: 3600, // 1 hour
                    ResponseContentType: 'video/mp4', // Ensure proper content type
                    ResponseCacheControl: 'max-age=3600' // Cache for 1 hour
                });

                console.log("✅ Generated signed URL for:", key);
                return res.json({ success: true, signedUrl });

            } catch (error) {
                console.error("❌ Error generating signed URL:", error.message);
                return res.json({ success: true, signedUrl: videoUrl }); // Fallback to original URL
            }
        }

        // For non-S3 URLs, return as-is
        res.json({ success: true, signedUrl: videoUrl });

    } catch (error) {
        console.error("❌ Error in signed URL generation:", error.message);
        res.status(500).json({ success: false, message: "Internal server error" });
    }
});

// Enhanced video proxy route to handle CORS and format conversion
router.get("/video-proxy", async (req, res) => {
    try {
        const { url, convert } = req.query;

        if (!url) {
            return res.status(400).json({ success: false, message: "Video URL is required" });
        }

        console.log("🎥 Proxying video request for:", url);

        // Get file extension to determine if conversion is needed
        const extension = url.split('.').pop().toLowerCase();
        const needsConversion = ['mkv', 'avi', 'wmv'].includes(extension);
        const forceConvert = convert === 'true';

        // Set appropriate headers for video streaming with enhanced CORS
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization');
        res.setHeader('Access-Control-Expose-Headers', 'Content-Range, Accept-Ranges, Content-Length');
        res.setHeader('Accept-Ranges', 'bytes');

        // Handle OPTIONS preflight requests
        if (req.method === 'OPTIONS') {
            return res.status(200).end();
        }

        // If conversion is needed or forced, use FFmpeg to convert on-the-fly
        if (needsConversion || forceConvert) {
            console.log("🔄 Converting video format on-the-fly:", extension, "-> mp4");

            try {
                // Set content type to MP4
                res.setHeader('Content-Type', 'video/mp4');

                // Use FFmpeg to convert and stream
                const ffmpeg = require('fluent-ffmpeg');
                const ffmpegPath = require('ffmpeg-static');
                ffmpeg.setFfmpegPath(ffmpegPath);

                const command = ffmpeg(url)
                    .format('mp4')
                    .videoCodec('libx264')
                    .audioCodec('aac')
                    .outputOptions([
                        '-movflags', 'frag_keyframe+empty_moov',
                        '-preset', 'ultrafast',
                        '-crf', '28',
                        '-maxrate', '2M',
                        '-bufsize', '4M'
                    ])
                    .on('start', (commandLine) => {
                        console.log('🎬 FFmpeg started:', commandLine);
                    })
                    .on('error', (err) => {
                        console.error('❌ FFmpeg error:', err.message);
                        if (!res.headersSent) {
                            res.status(500).json({ success: false, message: "Video conversion failed" });
                        }
                    })
                    .on('end', () => {
                        console.log('✅ FFmpeg conversion completed');
                    });

                // Stream the converted video directly to response
                command.pipe(res, { end: true });

            } catch (conversionError) {
                console.error("❌ Video conversion error:", conversionError.message);
                // Fallback to direct proxy
                return proxyDirectly();
            }
        } else {
            // Direct proxy for supported formats
            return proxyDirectly();
        }

        function proxyDirectly() {
            console.log("📡 Direct proxy for supported format");

            const axios = require('axios');

            // Forward the request with proper headers
            const headers = {
                'User-Agent': 'Mozilla/5.0 (compatible; VideoProxy/1.0)',
                'Accept': 'video/*,*/*;q=0.9',
                'Accept-Encoding': 'identity'
            };

            // Handle range requests
            if (req.headers.range) {
                headers['Range'] = req.headers.range;
            }

            axios({
                method: 'GET',
                url: url,
                responseType: 'stream',
                headers: headers,
                timeout: 30000
            }).then(response => {
                // Forward status code
                res.status(response.status);

                // Forward relevant headers
                const relevantHeaders = ['content-length', 'content-type', 'content-range', 'accept-ranges', 'last-modified', 'etag'];
                relevantHeaders.forEach(header => {
                    if (response.headers[header]) {
                        res.setHeader(header, response.headers[header]);
                    }
                });

                // Pipe the response
                response.data.pipe(res);

            }).catch(error => {
                console.error("❌ Direct proxy error:", error.message);
                if (!res.headersSent) {
                    res.status(500).json({ success: false, message: "Failed to proxy video" });
                }
            });
        }

    } catch (error) {
        console.error("❌ Video proxy error:", error.message);
        if (!res.headersSent) {
            res.status(500).json({ success: false, message: "Internal server error" });
        }
    }
});

// ============================================================================
// SUBTITLE MANAGEMENT ENDPOINTS
// ============================================================================

// Generate subtitles for a video
router.post("/generate-subtitles/:videoId", authMiddleware, async (req, res) => {
    try {
        const { videoId } = req.params;
        const { language = 'en' } = req.body;

        console.log(`🎬 Generating subtitles for video: ${videoId}, language: ${language}`);

        // Find the video
        const video = await Videos.findById(videoId);
        if (!video) {
            return res.status(404).json({
                success: false,
                message: "Video not found"
            });
        }

        // Check if video has a URL
        if (!video.videoUrl) {
            return res.status(400).json({
                success: false,
                message: "Video URL not available for subtitle generation"
            });
        }

        // Update subtitle generation status
        await Videos.findByIdAndUpdate(videoId, {
            subtitleGenerationStatus: "processing"
        });

        try {
            // Generate subtitles
            const subtitleData = await subtitleService.generateSubtitlesFromS3Video(
                video.videoUrl,
                videoId,
                language
            );

            // Update video with subtitle information
            const updatedVideo = await Videos.findByIdAndUpdate(
                videoId,
                {
                    $push: { subtitles: subtitleData },
                    hasSubtitles: true,
                    subtitleGenerationStatus: "completed"
                },
                { new: true }
            );

            console.log(`✅ Subtitles generated successfully for video: ${videoId}`);

            res.status(200).json({
                success: true,
                message: "Subtitles generated successfully",
                data: {
                    video: updatedVideo,
                    subtitle: subtitleData
                }
            });

        } catch (subtitleError) {
            console.error("❌ Subtitle generation failed:", subtitleError.message);

            // Update status to failed
            await Videos.findByIdAndUpdate(videoId, {
                subtitleGenerationStatus: "failed"
            });

            res.status(500).json({
                success: false,
                message: "Failed to generate subtitles",
                error: subtitleError.message
            });
        }

    } catch (error) {
        console.error("Error in subtitle generation:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
            error: error.message
        });
    }
});

// Upload custom subtitle file
router.post("/upload-subtitle/:videoId", authMiddleware, upload.single("subtitle"), async (req, res) => {
    try {
        const { videoId } = req.params;
        const { language = 'en', languageName, isDefault = false } = req.body;
        const subtitleFile = req.file;

        if (!subtitleFile) {
            return res.status(400).json({
                success: false,
                message: "Subtitle file is required"
            });
        }

        console.log(`📤 Uploading custom subtitle for video: ${videoId}, language: ${language}`);

        // Find the video
        const video = await Videos.findById(videoId);
        if (!video) {
            return res.status(404).json({
                success: false,
                message: "Video not found"
            });
        }

        // Validate subtitle file format
        const subtitleContent = subtitleFile.buffer.toString('utf-8');
        if (!subtitleService.validateSRTFormat(subtitleContent)) {
            return res.status(400).json({
                success: false,
                message: "Invalid SRT subtitle format"
            });
        }

        // Upload subtitle to S3
        const subtitleFilename = `StudyMaterials/Subtitles/${videoId}_${language}_custom.srt`;
        const subtitleUrl = await subtitleService.uploadSubtitleToS3(subtitleContent, subtitleFilename);

        // Prepare subtitle data
        const subtitleData = {
            language: language,
            languageName: languageName || subtitleService.getLanguageName(language),
            url: subtitleUrl,
            isDefault: isDefault === 'true' || isDefault === true,
            isAutoGenerated: false
        };

        // Check if subtitle for this language already exists
        const existingSubtitleIndex = video.subtitles.findIndex(sub => sub.language === language);

        let updatedVideo;
        if (existingSubtitleIndex !== -1) {
            // Update existing subtitle
            video.subtitles[existingSubtitleIndex] = subtitleData;
            updatedVideo = await video.save();
        } else {
            // Add new subtitle
            updatedVideo = await Videos.findByIdAndUpdate(
                videoId,
                {
                    $push: { subtitles: subtitleData },
                    hasSubtitles: true
                },
                { new: true }
            );
        }

        console.log(`✅ Custom subtitle uploaded successfully for video: ${videoId}`);

        res.status(200).json({
            success: true,
            message: "Subtitle uploaded successfully",
            data: {
                video: updatedVideo,
                subtitle: subtitleData
            }
        });

    } catch (error) {
        console.error("Error uploading subtitle:", error);
        res.status(500).json({
            success: false,
            message: "Failed to upload subtitle",
            error: error.message
        });
    }
});

// Get single video with subtitle information
router.get("/video/:videoId", authMiddleware, async (req, res) => {
    try {
        const { videoId } = req.params;

        const video = await Videos.findById(videoId);
        if (!video) {
            return res.status(404).json({
                success: false,
                message: "Video not found"
            });
        }

        res.status(200).json({
            success: true,
            data: video
        });
    } catch (error) {
        console.error("Error fetching video:", error);
        res.status(500).json({
            success: false,
            message: "Failed to fetch video",
            error: error.message
        });
    }
});

// Delete subtitle
router.delete("/subtitle/:videoId/:language", authMiddleware, async (req, res) => {
    try {
        const { videoId, language } = req.params;

        const video = await Videos.findById(videoId);
        if (!video) {
            return res.status(404).json({
                success: false,
                message: "Video not found"
            });
        }

        // Remove subtitle from array
        video.subtitles = video.subtitles.filter(sub => sub.language !== language);

        // Update hasSubtitles flag
        video.hasSubtitles = video.subtitles.length > 0;

        await video.save();

        res.status(200).json({
            success: true,
            message: "Subtitle deleted successfully",
            data: video
        });
    } catch (error) {
        console.error("Error deleting subtitle:", error);
        res.status(500).json({
            success: false,
            message: "Failed to delete subtitle",
            error: error.message
        });
    }
});

// Get all videos with subtitle status
router.get("/videos-subtitle-status", authMiddleware, async (req, res) => {
    try {
        const videos = await Videos.find({}, {
            title: 1,
            subject: 1,
            className: 1,
            level: 1,
            videoUrl: 1,
            videoID: 1,
            subtitles: 1,
            hasSubtitles: 1,
            subtitleGenerationStatus: 1,
            createdAt: 1
        }).sort({ createdAt: -1 });

        res.status(200).json({
            success: true,
            data: videos
        });
    } catch (error) {
        console.error("Error fetching videos with subtitle status:", error);
        res.status(500).json({
            success: false,
            message: "Failed to fetch videos",
            error: error.message
        });
    }
});

module.exports = router;

