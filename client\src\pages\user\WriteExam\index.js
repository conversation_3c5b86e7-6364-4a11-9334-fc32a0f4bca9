import { message } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { motion } from "framer-motion";
import { getExamById } from "../../../apicalls/exams";
import { addReport } from "../../../apicalls/reports";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import Instructions from "./Instructions";
import Pass from "../../../assets/pass.gif";
import Fail from "../../../assets/fail.gif";
import Confetti from "react-confetti";
import useWindowSize from "react-use/lib/useWindowSize";
import PassSound from "../../../assets/pass.mp3";
import FailSound from "../../../assets/fail.mp3";
import { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from "../../../apicalls/chat";
import XPResultDisplay from "../../../components/modern/XPResultDisplay";

// Simple Quiz Renderer Component - Safe from object rendering issues
const SimpleQuizRenderer = ({ question, questionIndex, totalQuestions, selectedAnswer, onAnswerSelect, onNext, onPrevious, timeLeft, examTitle }) => {
  if (!question || typeof question !== 'object') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center">
        <div className="bg-white rounded-xl p-8 shadow-lg text-center">
          <h3 className="text-xl font-bold text-red-600 mb-4">Question Error</h3>
          <p className="text-red-500">Unable to load question data.</p>
        </div>
      </div>
    );
  }

  // Safely extract and convert all properties to strings
  const questionText = String(question.name || '');
  const answerType = String(question.answerType || '');
  const correctOption = String(question.correctOption || '');
  const correctAnswer = String(question.correctAnswer || '');
  const imageUrl = question.image || question.imageUrl || '';

  // Safely process options
  let optionsArray = [];
  if (question.options) {
    try {
      if (Array.isArray(question.options)) {
        optionsArray = question.options
          .filter(opt => opt && typeof opt === 'string')
          .map(opt => String(opt).trim())
          .filter(opt => opt.length > 0);
      } else if (typeof question.options === 'object') {
        optionsArray = Object.values(question.options)
          .filter(opt => opt && typeof opt === 'string')
          .map(opt => String(opt).trim())
          .filter(opt => opt.length > 0);
      }
    } catch (error) {
      console.error('Error processing options:', error);
      optionsArray = [];
    }
  }

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const isTimeWarning = timeLeft <= 60;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold">Q</span>
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">{String(examTitle)}</h1>
                <p className="text-sm text-gray-500">Question {questionIndex + 1} of {totalQuestions}</p>
              </div>
            </div>

            {/* Timer */}
            <div className={`px-6 py-3 rounded-xl font-mono font-bold text-white shadow-lg ${
              isTimeWarning ? 'bg-red-600 animate-pulse' : 'bg-blue-600'
            }`}>
              <div className="text-xs mb-1">TIME</div>
              <div className="text-lg">{formatTime(timeLeft)}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Question Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-6">
          {/* Question Number */}
          <div className="mb-6">
            <div className="inline-flex items-center bg-blue-100 rounded-full px-4 py-2">
              <span className="text-blue-800 font-semibold">Question {questionIndex + 1} of {totalQuestions}</span>
            </div>
          </div>

          {/* Question Text */}
          <div className="mb-8">
            <h2 className="text-xl font-medium text-gray-900 leading-relaxed">
              {questionText}
            </h2>
          </div>

          {/* Image if present */}
          {imageUrl && (
            <div className="mb-8 text-center">
              <div className="inline-block rounded-xl overflow-hidden shadow-lg border border-gray-200">
                <img
                  src={imageUrl}
                  alt="Question"
                  className="max-w-full max-h-96 object-contain"
                />
              </div>
            </div>
          )}

          {/* Answer Options */}
          <div className="space-y-4">
            {answerType === "Options" && optionsArray.length > 0 ? (
              // Multiple Choice Questions
              optionsArray.map((option, index) => {
                const optionLetter = String.fromCharCode(65 + index); // A, B, C, D
                const isSelected = selectedAnswer === index;

                return (
                  <button
                    key={index}
                    onClick={() => onAnswerSelect(index)}
                    className={`w-full text-left p-4 rounded-xl border-2 transition-all duration-300 ${
                      isSelected
                        ? 'border-blue-500 bg-blue-50 text-blue-900'
                        : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'
                    }`}
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm ${
                        isSelected
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        {optionLetter}
                      </div>
                      <span className="flex-1 font-medium">{String(option)}</span>
                      {isSelected && (
                        <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </button>
                );
              })
            ) : (
              // Fill in the blank / Free text
              <div className="space-y-4">
                <label className="block text-sm font-medium text-gray-700">
                  Your Answer:
                </label>
                <input
                  type="text"
                  value={selectedAnswer || ''}
                  onChange={(e) => onAnswerSelect(e.target.value)}
                  placeholder="Type your answer here..."
                  className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-300 text-lg"
                />
              </div>
            )}
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center">
          <button
            onClick={onPrevious}
            disabled={questionIndex === 0}
            className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
              questionIndex === 0
                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                : 'bg-gray-600 text-white hover:bg-gray-700 shadow-lg hover:shadow-xl'
            }`}
          >
            ← Previous
          </button>

          <div className="text-center">
            <div className="text-sm text-gray-500 mb-2">Progress</div>
            <div className="w-48 bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((questionIndex + 1) / totalQuestions) * 100}%` }}
              ></div>
            </div>
          </div>

          <button
            onClick={onNext}
            className="px-6 py-3 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            {questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next →'}
          </button>
        </div>
      </div>
    </div>
  );
};

// Simple Review Renderer Component - Safe from object rendering issues
const SimpleReviewRenderer = ({
  questions,
  selectedOptions,
  explanations,
  fetchExplanation,
  setView,
  examData,
  setSelectedQuestionIndex,
  setSelectedOptions,
  setResult,
  setTimeUp,
  setSecondsLeft,
  setExplanations
}) => {
  if (!questions || !Array.isArray(questions)) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center">
        <div className="bg-white rounded-xl p-8 shadow-lg text-center">
          <h3 className="text-xl font-bold text-red-600 mb-4">Review Error</h3>
          <p className="text-red-500">No questions available for review.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-6">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-lg border border-slate-200/50">
            <h2 className="text-2xl font-bold text-blue-600 mb-2">Answer Review</h2>
            <p className="text-slate-600">Review your answers and get explanations</p>
          </div>
        </div>

        {/* Questions Review */}
        <div className="space-y-4 mb-6">
          {questions.map((question, index) => {
            // Safety check
            if (!question || typeof question !== 'object') {
              return null;
            }

            // Safely extract data
            const questionText = String(question.name || '');
            const answerType = String(question.answerType || '');
            const correctOption = question.correctOption;
            const correctAnswer = question.correctAnswer;
            const userAnswer = selectedOptions[index];

            // Determine if answer is correct
            let isCorrect = false;
            let correctAnswerText = '';
            let userAnswerText = '';

            if (answerType === "Options") {
              isCorrect = correctOption === userAnswer;

              // Get correct answer text
              if (question.options && correctOption !== undefined) {
                const optionValue = question.options[correctOption];
                correctAnswerText = typeof optionValue === 'string' ? optionValue : String(optionValue || correctOption || "Unknown");
              } else {
                correctAnswerText = String(correctOption || "Unknown");
              }

              // Get user answer text
              if (question.options && userAnswer !== undefined) {
                const optionValue = question.options[userAnswer];
                userAnswerText = typeof optionValue === 'string' ? optionValue : String(optionValue || userAnswer || "Not answered");
              } else {
                userAnswerText = String(userAnswer || "Not answered");
              }
            } else {
              isCorrect = correctAnswer === userAnswer;
              correctAnswerText = String(correctAnswer || "Unknown");
              userAnswerText = String(userAnswer || "Not answered");
            }

            return (
              <div
                key={String(question._id || index)}
                className={`rounded-lg shadow-md border-2 p-4 ${
                  isCorrect
                    ? 'bg-green-50 border-green-300'
                    : 'bg-red-50 border-red-300'
                }`}
              >
                {/* Question */}
                <div className="mb-3">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 rounded-lg flex items-center justify-center font-bold text-white text-sm bg-blue-600 flex-shrink-0 mt-1">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <p className="text-slate-800 font-medium leading-relaxed">{questionText}</p>
                    </div>
                  </div>
                </div>

                {/* Your Answer */}
                <div className="mb-2">
                  <span className="text-sm font-semibold text-slate-600">Your Answer: </span>
                  <span className={`font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>
                    {userAnswerText}
                  </span>
                  {isCorrect ? (
                    <span className="ml-3 text-green-600 text-xl">✓</span>
                  ) : (
                    <span className="ml-3 text-red-600 text-xl">✗</span>
                  )}
                </div>

                {/* Correct Answer (only for wrong answers) */}
                {!isCorrect && (
                  <div className="mb-2">
                    <span className="text-sm font-semibold text-slate-600">Correct Answer: </span>
                    <span className="font-medium text-green-700">{correctAnswerText}</span>
                    <span className="ml-3 text-green-500 text-xl">✓</span>
                  </div>
                )}

                {/* Explanation Button (only for wrong answers) */}
                {!isCorrect && (
                  <div className="mt-2">
                    <button
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2"
                      onClick={() => {
                        fetchExplanation(
                          questionText,
                          correctAnswerText,
                          userAnswerText,
                          question.image || question.imageUrl || ''
                        );
                      }}
                    >
                      <span>💡</span>
                      <span>Get Explanation</span>
                    </button>
                  </div>
                )}

                {/* Explanation Display */}
                {explanations[questionText] && (
                  <div className="mt-3 p-3 bg-white rounded-lg border-l-4 border-l-blue-500 shadow-sm border border-gray-200">
                    <div className="flex items-center mb-2">
                      <span className="text-blue-600 text-lg mr-2">💡</span>
                      <h6 className="font-bold text-gray-800 text-base">Explanation</h6>
                    </div>

                    {/* Image if present */}
                    {(question.image || question.imageUrl) && (
                      <div className="mb-3 p-2 bg-gray-50 rounded border border-gray-200">
                        <div className="flex items-center mb-1">
                          <span className="text-gray-700 text-sm font-medium">📊 Reference Diagram:</span>
                        </div>
                        <div className="flex justify-center">
                          <img
                            src={question.image || question.imageUrl}
                            alt="Question diagram"
                            className="max-w-full max-h-48 object-contain rounded border border-gray-300"
                            style={{ maxWidth: '350px' }}
                          />
                        </div>
                      </div>
                    )}

                    <div className="text-sm text-gray-800 leading-relaxed bg-gray-50 p-2 rounded">
                      {String(explanations[questionText] || '')}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Navigation */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <button
            className="px-8 py-4 bg-gray-600 hover:bg-gray-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg"
            onClick={() => setView("result")}
          >
            ← Back to Results
          </button>

          <button
            className="px-8 py-4 bg-green-600 hover:bg-green-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg"
            onClick={() => {
              // Reset exam state and restart
              setView("instructions");
              setSelectedQuestionIndex(0);
              setSelectedOptions({});
              setResult({});
              setTimeUp(false);
              setSecondsLeft(examData?.duration || 0);
              setExplanations({});
            }}
          >
            🔄 Retake Quiz
          </button>
        </div>
      </div>
    </div>
  );
};

function WriteExam() {
  const [examData, setExamData] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [result, setResult] = useState({});
  const params = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [view, setView] = useState("instructions");
  const [secondsLeft, setSecondsLeft] = useState(0);
  const [timeUp, setTimeUp] = useState(false);
  const [intervalId, setIntervalId] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [startTime, setStartTime] = useState(null);
  const { user } = useSelector((state) => state.user);

  const { width, height } = useWindowSize();
  const [explanations, setExplanations] = useState({});

  const getExamData = useCallback(async () => {
    try {
      setIsLoading(true);
      dispatch(ShowLoading());
      console.log("Fetching exam data for ID:", params.id);

      const response = await getExamById({ examId: params.id });
      console.log("Exam API Response:", response);

      dispatch(HideLoading());
      setIsLoading(false);

      if (response.success) {
        const examData = response.data;

        // Check different possible question locations
        let questions = [];
        if (examData?.questions && Array.isArray(examData.questions)) {
          questions = examData.questions;
        } else if (examData?.question && Array.isArray(examData.question)) {
          questions = examData.question;
        } else if (examData && Array.isArray(examData)) {
          questions = examData;
        }

        console.log("Exam Data:", examData);
        console.log("Questions found:", questions.length);
        console.log("Exam Data structure:", Object.keys(examData || {}));

        setQuestions(questions);
        setExamData(examData);
        setSecondsLeft(examData?.duration || 0);

        if (questions.length === 0) {
          console.warn("No questions found in exam data");
          console.log("Full response for debugging:", response);
          message.warning("This exam has no questions. Please contact your instructor.");
        }
      } else {
        console.error("API Error:", response.message);
        console.log("Full error response:", response);
        message.error(response.message || "Failed to load exam data");
      }
    } catch (error) {
      dispatch(HideLoading());
      setIsLoading(false);
      console.error("Exception in getExamData:", error);
      message.error(error.message || "Failed to load exam. Please try again.");
    }
  }, [params.id, dispatch]);

  const checkFreeTextAnswers = async (payload) => {
    if (!payload.length) return [];
    const { data } = await chatWithChatGPTToGetAns(payload);
    return data;
  };

  const calculateResult = useCallback(async () => {
    try {
      // Check if user is available
      if (!user || !user._id) {
        message.error("User not found. Please log in again.");
        navigate("/login");
        return;
      }

      dispatch(ShowLoading());

      const freeTextPayload = [];
      const indexMap = [];

      questions.forEach((q, idx) => {
        if (q.answerType === "Free Text" || q.answerType === "Fill in the Blank") {
          indexMap.push(idx);
          freeTextPayload.push({
            question: q.name,
            expectedAnswer: q.correctAnswer || q.correctOption,
            userAnswer: selectedOptions[idx] || "",
          });
        }
      });

      const gptResults = await checkFreeTextAnswers(freeTextPayload);
      const gptMap = {};

      gptResults.forEach((r) => {
        if (r.result && typeof r.result.isCorrect === "boolean") {
          gptMap[r.question] = r.result;
        } else if (typeof r.isCorrect === "boolean") {
          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || "" };
        }
      });

      const correctAnswers = [];
      const wrongAnswers = [];
      const wrongPayload = [];

      questions.forEach((q, idx) => {
        const userAnswerKey = selectedOptions[idx] || "";

        if (q.answerType === "Free Text" || q.answerType === "Fill in the Blank") {
          const { isCorrect = false, reason = "" } = gptMap[q.name] || {};
          const enriched = { ...q, userAnswer: userAnswerKey, reason };

          if (isCorrect) {
            correctAnswers.push(enriched);
          } else {
            wrongAnswers.push(enriched);
            wrongPayload.push({
              question: q.name,
              expectedAnswer: q.correctAnswer || q.correctOption,
              userAnswer: userAnswerKey,
            });
          }
        } else if (q.answerType === "Options") {
          const correctKey = q.correctOption;
          const correctValue = (q.options && q.options[correctKey]) || correctKey;
          const userValue = (q.options && q.options[userAnswerKey]) || userAnswerKey || "";

          const isCorrect = correctKey === userAnswerKey;
          const enriched = { ...q, userAnswer: userAnswerKey };

          if (isCorrect) {
            correctAnswers.push(enriched);
          } else {
            wrongAnswers.push(enriched);
            wrongPayload.push({
              question: q.name,
              expectedAnswer: correctValue,
              userAnswer: userValue,
            });
          }
        }
      });

      // Calculate time spent
      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;
      const totalTimeAllowed = (examData?.duration || 0) * 60; // Convert minutes to seconds

      // Calculate score and points
      const totalQuestions = questions.length;
      const correctCount = correctAnswers.length;
      const scorePercentage = Math.round((correctCount / totalQuestions) * 100);
      const points = correctCount * 10; // 10 points per correct answer

      // Determine pass/fail based on percentage
      const passingPercentage = examData.passingMarks || 70; // Default 70%
      const verdict = scorePercentage >= passingPercentage ? "Pass" : "Fail";

      const tempResult = {
        correctAnswers: correctAnswers || [],
        wrongAnswers: wrongAnswers || [],
        verdict: verdict || "Fail",
        score: scorePercentage,
        points: points,
        totalQuestions: totalQuestions,
        timeSpent: timeSpent,
        totalTimeAllowed: totalTimeAllowed
      };

      setResult(tempResult);

      const response = await addReport({
        exam: params.id,
        result: tempResult,
        user: user._id,
      });

      if (response.success) {
        // Include XP data in the result
        const resultWithXP = {
          ...tempResult,
          xpData: response.xpData
        };
        setResult(resultWithXP);

        setView("result");
        window.scrollTo(0, 0);
        new Audio(verdict === "Pass" ? PassSound : FailSound).play();
      } else {
        message.error(response.message);
      }
      dispatch(HideLoading());

    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);

  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {
    try {
      dispatch(ShowLoading());
      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });
      dispatch(HideLoading());

      if (response.success) {
        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));
      } else {
        message.error(response.error || "Failed to fetch explanation.");
      }
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  };

  const startTimer = () => {
    const totalSeconds = examData?.duration || 0;
    setSecondsLeft(totalSeconds);
    setStartTime(Date.now()); // Record start time for XP calculation

    const newIntervalId = setInterval(() => {
      setSecondsLeft((prevSeconds) => {
        if (prevSeconds > 0) {
          return prevSeconds - 1;
        } else {
          setTimeUp(true);
          return 0;
        }
      });
    }, 1000);
    setIntervalId(newIntervalId);
  };

  useEffect(() => {
    if (timeUp && view === "questions") {
      clearInterval(intervalId);
      calculateResult();
    }
  }, [timeUp, view, intervalId, calculateResult]);

  useEffect(() => {
    console.log("WriteExam useEffect - params.id:", params.id);
    if (params.id) {
      getExamData();
    } else {
      console.error("No exam ID provided in URL parameters");
      message.error("Invalid exam ID. Please select a quiz from the list.");
      navigate('/user/quiz');
    }
  }, [params.id, getExamData, navigate]);

  useEffect(() => {
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [intervalId]);

  // Add fullscreen class for all quiz views (instructions, questions, results)
  useEffect(() => {
    if (view === "instructions" || view === "questions" || view === "result") {
      document.body.classList.add("quiz-fullscreen");
    } else {
      document.body.classList.remove("quiz-fullscreen");
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove("quiz-fullscreen");
    };
  }, [view]);

  // Repair function for fixing orphaned questions
  const repairExamQuestions = async () => {
    try {
      dispatch(ShowLoading());
      const response = await fetch('/api/exams/repair-exam-questions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ examId: params.id })
      });

      const data = await response.json();
      if (data.success) {
        message.success(data.message);
        // Reload the exam data
        getExamData();
      } else {
        message.error(data.message);
      }
    } catch (error) {
      message.error("Failed to repair exam questions");
    } finally {
      dispatch(HideLoading());
    }
  };

  // Check if user is authenticated
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center">
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4">
          <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Authentication Required</h2>
          <p className="text-gray-600 mb-8">Please log in to access the exam and start your learning journey.</p>
          <button
            className="w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg"
            onClick={() => navigate("/login")}
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return examData ? (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">

      {view === "instructions" && (
        <Instructions
          examData={examData}
          setView={setView}
          startTimer={startTimer}
          questions={questions}
        />
      )}

      {view === "questions" && (
        isLoading ? (
          <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
            <div className="bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-blue-200 max-w-lg mx-4 text-center">
              <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg animate-pulse">
                <svg className="w-12 h-12 text-white animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-blue-800 mb-4">Loading Quiz...</h3>
              <p className="text-blue-600 text-lg">
                Please wait while we prepare your questions.
              </p>
            </div>
          </div>
        ) : questions.length === 0 ? (
          <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center">
            <div className="bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center">
              <div className="w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                <svg className="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-amber-800 mb-4">No Questions Found</h3>
              <p className="text-amber-700 mb-6 text-lg leading-relaxed">
                This exam appears to have no questions. This could be due to:
              </p>
              <ul className="text-left text-amber-700 mb-8 space-y-2">
                <li>• Questions not properly linked to this exam</li>
                <li>• Database connection issues</li>
                <li>• Exam configuration problems</li>
              </ul>
              <div className="space-y-3">
                <button
                  onClick={repairExamQuestions}
                  className="w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg"
                >
                  🔧 Repair Questions
                </button>
                <button
                  onClick={() => {
                    console.log("Retrying exam data fetch...");
                    getExamData();
                  }}
                  className="w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg"
                >
                  🔄 Retry Loading
                </button>
                <button
                  onClick={() => navigate('/user/quiz')}
                  className="w-full px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg"
                >
                  ← Back to Quiz List
                </button>
              </div>
            </div>
          </div>
        ) : (
          <SimpleQuizRenderer
            question={questions[selectedQuestionIndex]}
            questionIndex={selectedQuestionIndex}
            totalQuestions={questions.length}
            selectedAnswer={selectedOptions[selectedQuestionIndex]}
            onAnswerSelect={(answer) => setSelectedOptions({...selectedOptions, [selectedQuestionIndex]: answer})}
            onNext={() => {
              if (selectedQuestionIndex === questions.length - 1) {
                calculateResult();
              } else {
                setSelectedQuestionIndex(selectedQuestionIndex + 1);
              }
            }}
            onPrevious={() => {
              if (selectedQuestionIndex > 0) {
                setSelectedQuestionIndex(selectedQuestionIndex - 1);
              }
            }}
            timeLeft={secondsLeft}
            examTitle={examData?.name || "Quiz"}
          />
        )
      )}

      {view === "result" && (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8">
          {result.verdict === "Pass" && <Confetti width={width} height={height} />}

          <div className="max-w-4xl mx-auto px-4">
            <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden">
              {/* Modern Header */}
              <div className={`px-8 py-10 text-center relative ${
                result.verdict === "Pass"
                  ? "bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10"
                  : "bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10"
              }`}>
                <div className="relative">
                  <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${
                    result.verdict === "Pass"
                      ? "bg-gradient-to-br from-emerald-500 to-green-600"
                      : "bg-gradient-to-br from-amber-500 to-orange-600"
                  }`}>
                    <img
                      src={result.verdict === "Pass" ? Pass : Fail}
                      alt={result.verdict}
                      className="w-12 h-12 object-contain"
                    />
                  </div>
                  <h1 className={`text-4xl font-black mb-4 tracking-tight ${
                    result.verdict === "Pass" ? "text-emerald-700" : "text-amber-700"
                  }`}>
                    {result.verdict === "Pass" ? "Excellent Work!" : "Keep Pushing!"}
                  </h1>
                  <p className="text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed">
                    {result.verdict === "Pass"
                      ? "You've mastered this exam with flying colors!"
                      : "Every challenge makes you stronger. Try again!"}
                  </p>
                </div>
              </div>

              {/* Modern Statistics Cards */}
              <div className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  {/* Score Card */}
                  <div className="group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative text-center">
                      <div className="text-4xl font-black text-blue-600 mb-2 tracking-tight">
                        {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%
                      </div>
                      <div className="text-sm font-bold text-blue-700/80 uppercase tracking-wider">Your Score</div>
                    </div>
                  </div>

                  {/* Correct vs Total */}
                  <div className="group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300">
                    <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative text-center">
                      <div className="text-4xl font-black text-emerald-600 mb-2 tracking-tight">
                        {result.correctAnswers?.length || 0}/{questions.length}
                      </div>
                      <div className="text-sm font-bold text-emerald-700/80 uppercase tracking-wider">Correct</div>
                    </div>
                  </div>

                  {/* Pass Status */}
                  <div className={`group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${
                    result.verdict === "Pass"
                      ? "bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50"
                      : "bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50"
                  }`}>
                    <div className={`absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${
                      result.verdict === "Pass" ? "from-emerald-500/5" : "from-amber-500/5"
                    } to-transparent`}></div>
                    <div className="relative text-center">
                      <div className={`text-4xl font-black mb-2 tracking-tight ${
                        result.verdict === "Pass" ? "text-emerald-600" : "text-amber-600"
                      }`}>
                        {result.verdict === "Pass" ? "PASS" : "RETRY"}
                      </div>
                      <div className={`text-sm font-bold uppercase tracking-wider ${
                        result.verdict === "Pass" ? "text-emerald-700/80" : "text-amber-700/80"
                      }`}>
                        {result.verdict === "Pass" ? "Success!" : `Need ${examData.passingMarks}`}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Modern Progress Visualization */}
                <div className="mb-8">
                  <div className="relative bg-slate-100 rounded-2xl p-6">
                    <div className="text-center mb-4">
                      <h3 className="text-lg font-bold text-slate-700 mb-1">Performance Overview</h3>
                      <p className="text-sm text-slate-500">Your achievement level</p>
                    </div>
                    <div className="relative">
                      <div className="w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden">
                        <div
                          className={`h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${
                            result.verdict === "Pass"
                              ? "bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500"
                              : "bg-gradient-to-r from-amber-500 via-orange-500 to-red-500"
                          }`}
                          style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
                        </div>
                      </div>
                      <div className="flex justify-between items-center mt-3">
                        <span className="text-xs font-medium text-slate-500">0%</span>
                        <span className={`text-lg font-black tracking-tight ${
                          result.verdict === "Pass" ? "text-emerald-600" : "text-amber-600"
                        }`}>
                          {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%
                        </span>
                        <span className="text-xs font-medium text-slate-500">100%</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* XP Display */}
                {result.xpData && (
                  <div className="mb-8">
                    <XPResultDisplay xpData={result.xpData} />
                  </div>
                )}

                {/* Modern Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button
                    className="group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    onClick={() => setView("review")}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <span className="relative">Review Answers</span>
                  </button>


                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {view === "review" && (
        <SimpleReviewRenderer
          questions={questions}
          selectedOptions={selectedOptions}
          explanations={explanations}
          fetchExplanation={fetchExplanation}
          setView={setView}
          examData={examData}
          setSelectedQuestionIndex={setSelectedQuestionIndex}
          setSelectedOptions={setSelectedOptions}
          setResult={setResult}
          setTimeUp={setTimeUp}
          setSecondsLeft={setSecondsLeft}
          setExplanations={setExplanations}
        />
      )}
    </div>
  ) : null;
}

export default WriteExam;