import React, { useState, useEffect } from 'react';
import '../pages/user/Quiz/responsive.css';

const QuizRenderer = ({
  question,
  questionIndex,
  totalQuestions,
  selectedAnswer,
  onAnswerChange,
  timeLeft,
  onNext,
  onPrevious,
  examTitle = "Quiz",
  isTimeWarning = false
}) => {
  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');
  const [isAnswered, setIsAnswered] = useState(false);

  useEffect(() => {
    setCurrentAnswer(selectedAnswer || '');
    setIsAnswered(!!selectedAnswer);
  }, [selectedAnswer, questionIndex]);

  const handleAnswerSelect = (answer) => {
    setCurrentAnswer(answer);
    setIsAnswered(true);
    onAnswerChange(answer);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;

  // Early return for invalid question
  if (!question || !question.name) {
    return (
      <div className="quiz-container quiz-responsive">
        <div className="quiz-content quiz-content-responsive">
          <div className="quiz-question-container">
            <div className="text-center text-red-600 p-6 bg-red-50 rounded-lg border border-red-200">
              <i className="ri-error-warning-line text-3xl mb-3 block"></i>
              <h3 className="text-lg font-semibold mb-2">Question Not Available</h3>
              <p className="text-sm">This question could not be loaded. Please try refreshing the page.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const renderMCQ = () => {
    if (!question || !question.options || Object.keys(question.options).length === 0) {
      return (
        <div className="quiz-question-container">
          <div className="text-center text-red-600 p-4 bg-red-50 rounded-lg border border-red-200">
            <i className="ri-error-warning-line text-2xl mb-2"></i>
            <p>No options available for this question.</p>
          </div>
        </div>
      );
    }

    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];

    return (
      <div className="quiz-options space-y-4">
        {Object.entries(question.options).map(([key, value], index) => {
          const optionKey = String(key).trim();
          const optionValue = String(value || '').trim();
          const label = optionLabels[index] || optionKey;
          const isSelected = currentAnswer === optionKey;

          // Skip empty options
          if (!optionValue) return null;

          return (
            <button
              key={optionKey}
              onClick={() => handleAnswerSelect(optionKey)}
              className={`quiz-option w-full text-left transition-all duration-300 transform hover:scale-[1.02] ${
                isSelected
                  ? 'selected bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg border-blue-500'
                  : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800'
              } border-2 rounded-xl p-4 sm:p-5 group relative overflow-hidden`}
            >
              <div className="flex items-center space-x-4">
                <div className={`quiz-option-letter flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm transition-all duration-300 ${
                  isSelected
                    ? 'bg-white text-blue-600 shadow-md'
                    : 'bg-gradient-to-br from-blue-100 to-indigo-100 text-blue-700 group-hover:from-blue-200 group-hover:to-indigo-200'
                }`}>
                  {label}
                </div>
                <span className={`quiz-option-text flex-1 font-medium transition-all duration-300 ${
                  isSelected ? 'text-white' : 'text-gray-800 group-hover:text-blue-800'
                }`}>
                  {optionValue}
                </span>
                {isSelected && (
                  <div className="flex-shrink-0 w-6 h-6 bg-white rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </div>
              {!isSelected && (
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/0 to-indigo-500/0 group-hover:from-blue-500/5 group-hover:to-indigo-500/5 transition-all duration-300 rounded-xl"></div>
              )}
            </button>
          );
        })}
      </div>
    );
  };

  const renderFillBlank = () => (
    <div className="quiz-question-container space-y-6">
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <div className="flex items-center space-x-2">
            <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
            <span>Your Answer:</span>
          </div>
        </label>
        <div className="relative">
          <input
            type="text"
            value={currentAnswer}
            onChange={(e) => handleAnswerSelect(e.target.value)}
            placeholder="Type your answer here..."
            className="quiz-fill-input w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-300 text-lg font-medium bg-white shadow-sm"
          />
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
            {currentAnswer ? (
              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            ) : (
              <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
              </div>
            )}
          </div>
        </div>
      </div>

      {currentAnswer && (
        <div className="bg-gradient-to-r from-emerald-50 to-green-50 rounded-xl p-4 sm:p-6 border border-emerald-200 shadow-sm">
          <div className="flex items-center space-x-3 sm:space-x-4">
            <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-500 rounded-full flex items-center justify-center shadow-sm">
              <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <p className="text-emerald-800 font-semibold text-sm">Your Answer:</p>
              <p className="text-emerald-900 font-bold text-lg">{currentAnswer}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderImageQuestion = () => (
    <div className="space-y-8">
      {question.imageUrl && (
        <div className="text-center">
          <div className="inline-block p-2 bg-white rounded-xl shadow-lg border border-gray-200">
            <img
              src={question.imageUrl}
              alt="Question diagram"
              className="max-w-full max-h-96 rounded-lg mx-auto"
            />
          </div>
        </div>
      )}

      {question.options ? renderMCQ() : renderFillBlank()}
    </div>
  );

  return (
    <div className="quiz-container quiz-responsive min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Enhanced Progress Bar */}
      <div className="quiz-progress-bar relative">
        <div className="absolute inset-0 bg-gray-200 rounded-full"></div>
        <div
          className="quiz-progress-fill relative z-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full transition-all duration-500 ease-out"
          style={{ width: `${progressPercentage}%` }}
        >
          <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-white rounded-full shadow-md"></div>
        </div>
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs font-medium text-gray-600">
          {Math.round(progressPercentage)}%
        </div>
      </div>

      {/* Enhanced Header */}
      <div className="quiz-progress-container">
        <div className="quiz-header-content-improved bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-4 sm:p-6">
          <div className="quiz-title-section">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm font-bold">Q</span>
              </div>
              <p className="quiz-subtitle text-sm sm:text-base font-semibold text-gray-800">
                {examTitle}
              </p>
            </div>
          </div>

          <div className="quiz-timer-center">
            <div className={`quiz-timer ${isTimeWarning ? 'warning animate-pulse' : ''} bg-gradient-to-r ${isTimeWarning ? 'from-red-600 to-red-700 border-red-300' : 'from-blue-600 to-indigo-700 border-blue-300'} text-white rounded-xl px-6 py-3 font-mono font-bold shadow-2xl border-2`} style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}>
              <span className="text-sm font-semibold block mb-1" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}>TIME</span>
              <div className="text-xl font-black" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.7)' }}>
                {formatTime(timeLeft)}
              </div>
            </div>
          </div>

          <div className="quiz-question-counter-right">
            <div className="bg-gradient-to-r from-gray-100 to-gray-200 rounded-xl px-4 py-2 text-center">
              <span className="font-bold text-gray-800">{questionIndex + 1} of {totalQuestions}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Question Content */}
      <div className="quiz-content pb-20 quiz-content-responsive px-4 sm:px-6">
        <div className="quiz-question-container max-w-4xl mx-auto">
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 sm:p-8 mb-6">
            <div className="quiz-question-number text-sm sm:text-base mb-4">
              <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full px-4 py-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="font-semibold text-blue-800">{questionIndex + 1} of {totalQuestions}</span>
              </div>
            </div>

            <div className="quiz-question-text text-lg sm:text-xl lg:text-2xl font-medium text-gray-800 leading-relaxed mb-6">
              {question.name}
            </div>

            {question.image && (
              <div className="quiz-image-container mb-6">
                <div className="relative rounded-xl overflow-hidden shadow-lg">
                  <img
                    src={question.image}
                    alt="Question"
                    className="quiz-image w-full h-auto max-h-96 object-contain bg-gray-50"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent pointer-events-none"></div>
                </div>
              </div>
            )}

            <div className="quiz-options-container">
              {question.answerType === "Options" && renderMCQ()}
              {(question.answerType === "Free Text" || question.answerType === "Fill in the Blank") && renderFillBlank()}
              {question.answerType === "Image" && question.imageUrl && renderImageQuestion()}
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Navigation */}
      <div className="quiz-navigation fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-t border-gray-200 p-4 sm:p-6 z-50">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <button
            onClick={onPrevious}
            disabled={questionIndex === 0}
            className={`quiz-nav-btn flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
              questionIndex === 0
                ? 'disabled bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'secondary bg-gray-200 hover:bg-gray-300 text-gray-700 hover:text-gray-900 transform hover:scale-105'
            }`}
            title={questionIndex === 0 ? 'This is the first question' : 'Go to previous question'}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span>Previous</span>
          </button>

          <div className="flex items-center space-x-3 text-sm text-gray-600">
            <div className="hidden sm:flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>{questionIndex + 1} of {totalQuestions}</span>
            </div>
            {!isAnswered && (
              <div className="flex items-center space-x-2 text-amber-600 bg-amber-50 px-3 py-1 rounded-full">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <span className="text-xs font-medium">Select an answer</span>
              </div>
            )}
          </div>

          <button
            onClick={onNext}
            disabled={!isAnswered}
            className={`quiz-nav-btn flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
              !isAnswered
                ? 'disabled bg-gray-100 text-gray-400 cursor-not-allowed'
                : questionIndex === totalQuestions - 1
                  ? 'primary bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg transform hover:scale-105'
                  : 'primary bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg transform hover:scale-105'
            }`}
            title={!isAnswered ? 'Please select an answer first' :
                   questionIndex === totalQuestions - 1 ? 'Submit your quiz' : 'Go to next question'}
          >
            {questionIndex === totalQuestions - 1 ? (
              <>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Submit Quiz</span>
              </>
            ) : (
              <>
                <span>Next</span>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </>
            )}
          </button>
        </div>
      </div>


    </div>
  );
};

export default QuizRenderer;
